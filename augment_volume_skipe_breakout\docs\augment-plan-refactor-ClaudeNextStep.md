# B<PERSON>ớ<PERSON> tiếp the<PERSON> - <PERSON><PERSON> hoạch hành động cụ thể

## Phase 0: Preparation (Tuần 1)

### Ngày 1-2: Setup Development Environment

```bash
# 1. Tạo virtual environment mới
python -m venv venv_refactor
source venv_refactor/bin/activate  # Linux/Mac
# hoặc venv_refactor\Scripts\activate  # Windows

# 2. Cài đặt dependencies
pip install pytest pytest-cov pytest-mock pytest-benchmark
pip install black isort flake8 mypy
pip install pre-commit
pip install pandas numpy matplotlib seaborn
pip install pydantic python-dotenv
pip install duckdb

# 3. Setup pre-commit hooks
pre-commit install
```

### Ngày 3-4: <PERSON><PERSON><PERSON> c<PERSON>u trúc thư mục mới

```bash
# Tạo cấu trúc thư mục mới song song với code cũ
mkdir -p refactored_src/{data_layer,core_engine,interfaces,utils}
mkdir -p refactored_src/data_layer/{fetchers,processors,storage}
mkdir -p refactored_src/core_engine/{indicators,patterns,strategies,screener,risk,portfolio}
mkdir -p refactored_src/interfaces/{cli,visualization}
mkdir -p refactored_src/utils/{config,logging,metrics}

# Tạo __init__.py files
find refactored_src -type d -exec touch {}/__init__.py \;

# Tạo test structure
mkdir -p tests/{unit,integration,e2e,performance,fixtures}
```

### Ngày 5-7: Thiết lập CI/CD và Quality Gates

```yaml
# .github/workflows/ci.yml
name: CI Pipeline

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install -r requirements-dev.txt
    
    - name: Run linters
      run: |
        black --check refactored_src tests
        isort --check refactored_src tests
        flake8 refactored_src tests
        mypy refactored_src
    
    - name: Run tests
      run: |
        pytest tests/unit --cov=refactored_src --cov-fail-under=80
    
    - name: Run integration tests
      run: |
        pytest tests/integration -m "not requires_api"
```

## Phase 1: Data Layer Implementation (Tuần 2-3)

### Sprint 2.2: Strategy Pattern Implementation

**Mục tiêu**: Tách VolumeBreakoutStrategy thành module độc lập

```python
# refactored_src/core_engine/strategies/base.py
from abc import ABC, abstractmethod
from typing import Dict, Any
import pandas as pd

class TradingStrategy(ABC):
    @abstractmethod
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def calculate_score(self, data: pd.DataFrame) -> float:
        pass

# refactored_src/core_engine/strategies/volume_breakout.py
from .base import TradingStrategy
from ..indicators.volume import VolumeIndicators
from ..patterns.consolidation import ConsolidationDetector

class VolumeBreakoutStrategy(TradingStrategy):
    def __init__(self, config):
        self.config = config
        self.volume_indicators = VolumeIndicators()
        self.consolidation_detector = ConsolidationDetector()
        
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        # Migrate logic từ VolumeBreakoutDetector.analyze()
        pass
```

**Timeline**: 4 ngày
**Deliverable**: Strategy với comprehensive unit tests

## Phase 3: Adapter Layer Implementation (Tuần 6)

### Sprint 3.1: Legacy Code Adapters

**Mục tiêu**: Tạo bridge between old và new code

```python
# adapters/legacy_bridge.py
from typing import Dict, Any, List
import pandas as pd
from refactored_src.data_layer.fetchers.vnstock_fetcher import VNStockFetcher
from refactored_src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy

class LegacyStockScreenerAdapter:
    """Adapter để StockScreener cũ có thể sử dụng components mới"""
    
    def __init__(self):
        self.new_fetcher = VNStockFetcher()
        self.new_strategy = VolumeBreakoutStrategy()
        
    def analyze_stocks_hybrid(self, symbols: List[str], use_new_logic: bool = False) -> Dict:
        """Phân tích với option sử dụng logic mới hoặc cũ"""
        if use_new_logic:
            return self._analyze_with_new_logic(symbols)
        else:
            # Fallback to old logic
            from volume_skipe_breakout.stock_screener import StockScreener
            old_screener = StockScreener()
            return old_screener.analyze_stocks(symbols)
    
    def _analyze_with_new_logic(self, symbols: List[str]) -> Dict:
        # Implement using new architecture
        pass

# migration_utils.py
class DataMigrationHelper:
    """Helper để migrate data từ format cũ sang format mới"""
    
    @staticmethod
    def convert_old_config_to_new(old_config) -> 'NewBreakoutConfig':
        # Convert BreakoutConfig cũ sang format mới
        pass
    
    @staticmethod
    def convert_analysis_result_format(old_result: Dict) -> Dict:
        # Convert kết quả analysis từ format cũ sang format mới
        pass
```

**Timeline**: 3 ngày

### Sprint 3.2: Feature Flags Implementation

```python
# refactored_src/utils/feature_flags.py
from enum import Enum
from typing import Dict, Any

class FeatureFlag(Enum):
    USE_NEW_DATA_LAYER = "use_new_data_layer"
    USE_NEW_STRATEGY_ENGINE = "use_new_strategy_engine"
    USE_NEW_CLI = "use_new_cli"

class FeatureFlagManager:
    def __init__(self, config: Dict[str, bool] = None):
        self.flags = config or {}
        
    def is_enabled(self, flag: FeatureFlag) -> bool:
        return self.flags.get(flag.value, False)
        
    def enable(self, flag: FeatureFlag):
        self.flags[flag.value] = True
        
    def disable(self, flag: FeatureFlag):
        self.flags[flag.value] = False

# Usage trong main application
feature_flags = FeatureFlagManager({
    "use_new_data_layer": True,  # Enable new data layer
    "use_new_strategy_engine": False,  # Keep using old strategy for now
    "use_new_cli": False  # Keep using old CLI
})
```

**Timeline**: 2 ngày

## Phase 4: Testing & Validation (Tuần 7)

### Sprint 4.1: Comprehensive Test Coverage

**Mục tiêu**: Đạt 80%+ test coverage

```python
# tests/integration/test_new_vs_old_comparison.py
import pytest
from volume_skipe_breakout.stock_screener import StockScreener as OldScreener
from refactored_src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy as NewStrategy

class TestNewVsOldComparison:
    """So sánh kết quả giữa implementation cũ và mới"""
    
    @pytest.mark.integration
    def test_strategy_results_consistency(self, sample_stock_data):
        # Old implementation
        old_screener = OldScreener()
        old_result = old_screener.analyze_stocks(['TCB'], lookback_days=20)
        
        # New implementation
        new_strategy = NewStrategy()
        new_result = new_strategy.analyze(sample_stock_data)
        
        # Results should be within acceptable tolerance
        assert abs(old_result['score'] - new_result['score']) < 0.5
        
    @pytest.mark.integration 
    def test_data_fetching_consistency(self):
        """Verify new data fetcher returns same data as old one"""
        # Implementation to compare data consistency
        pass
```

**Timeline**: 4 ngày

### Sprint 4.2: Performance Benchmarking

```python
# tests/performance/benchmark_comparison.py
import pytest
import time
from memory_profiler import profile

class TestPerformanceBenchmarks:
    @pytest.mark.benchmark
    def test_strategy_execution_speed(self, benchmark, large_dataset):
        def execute_new_strategy():
            from refactored_src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy
            strategy = VolumeBreakoutStrategy()
            return strategy.analyze(large_dataset)
            
        result = benchmark(execute_new_strategy)
        assert result is not None
        
    @pytest.mark.benchmark  
    def test_memory_usage_comparison(self, large_dataset):
        """Compare memory usage between old and new implementation"""
        # Implementation for memory usage comparison
        pass
```

**Timeline**: 3 ngày

## Phase 5: Gradual Migration (Tuần 8-9)

### Sprint 5.1: CLI Migration với Backward Compatibility

```python
# refactored_src/interfaces/cli/main_cli.py
import click
from ..utils.feature_flags import FeatureFlagManager, FeatureFlag

@click.group()
@click.option('--use-new-engine', is_flag=True, help='Use new strategy engine')
@click.pass_context
def cli(ctx, use_new_engine):
    ctx.ensure_object(dict)
    ctx.obj['feature_flags'] = FeatureFlagManager({
        'use_new_strategy_engine': use_new_engine
    })

@cli.command()
@click.option('--symbols', help='Comma-separated list of symbols')
@click.option('--sector', help='Sector to analyze')
@click.pass_context
def scan(ctx, symbols, sector):
    feature_flags = ctx.obj['feature_flags']
    
    if feature_flags.is_enabled(FeatureFlag.USE_NEW_STRATEGY_ENGINE):
        # Use new implementation
        from refactored_src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy
        strategy = VolumeBreakoutStrategy()
        # ... implement new logic
    else:
        # Fallback to old implementation
        from volume_skipe_breakout.stock_screener import StockScreener
        screener = StockScreener()
        # ... use old logic
```

**Timeline**: 5 ngày

### Sprint 5.2: Documentation và Migration Guide

```markdown
# Migration Guide

## For End Users

### Using New Features Gradually

1. **Enable new data layer** (safe to enable):
   ```bash
   python main.py scan --use-new-data-layer --symbols TCB,VCB
   ```

2. **Enable new strategy engine** (experimental):
   ```bash
   python main.py scan --use-new-engine --symbols TCB,VCB
   ```

### Configuration Migration

Old config format:
```python
config = BreakoutConfig(
    volume_threshold=1.5,
    lookback_period=10
)
```

New config format:
```yaml
# config.yaml
trading:
  volume_threshold: 1.5
  lookback_period: 10
```

## For Developers

### Adding New Strategies

```python
# 1. Create new strategy class
class MyNewStrategy(TradingStrategy):
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        # Implementation
        pass

# 2. Register with strategy factory
StrategyFactory.register('my_new_strategy', MyNewStrategy)

# 3. Use in CLI
python main.py scan --strategy my_new_strategy
```
```

**Timeline**: 4 ngày

## Monitoring & Rollback Plan

### Metrics to Track
```python
# refactored_src/utils/migration_metrics.py
class MigrationMetrics:
    def __init__(self):
        self.old_execution_times = []
        self.new_execution_times = []
        self.error_rates = {'old': 0, 'new': 0}
        self.user_adoption = {'new_features': 0, 'total_usage': 0}
    
    def track_execution_time(self, implementation: str, duration: float):
        if implementation == 'old':
            self.old_execution_times.append(duration)
        else:
            self.new_execution_times.append(duration)
    
    def get_performance_comparison(self) -> Dict[str, float]:
        return {
            'old_avg': sum(self.old_execution_times) / len(self.old_execution_times),
            'new_avg': sum(self.new_execution_times) / len(self.new_execution_times),
            'improvement': self.calculate_improvement()
        }
```

### Rollback Strategy
1. **Immediate rollback**: Disable feature flags
2. **Partial rollback**: Roll back specific modules
3. **Full rollback**: Revert to tagged old version

## Success Criteria

### Week 8 Goals:
- [ ] 80%+ test coverage for new modules
- [ ] Performance parity with old implementation
- [ ] Zero breaking changes for existing CLI users
- [ ] Documentation complete

### Week 9 Goals:
- [ ] 50% of analysis requests using new data layer
- [ ] 25% of analysis requests using new strategy engine
- [ ] All legacy adapters functioning correctly
- [ ] Migration metrics dashboard operational

## Risk Mitigation

### High Risk Items:
1. **Data inconsistency**: Implement thorough comparison tests
2. **Performance regression**: Continuous benchmarking
3. **User resistance**: Gradual rollout with opt-in flags
4. **API changes**: Rate limiting and circuit breakers

### Contingency Plans:
1. **Feature flags** để immediate rollback
2. **Canary deployment** cho new features
3. **A/B testing** cho critical paths
4. **Automated alerts** cho performance/error thresholds

## Daily Standup Template

### Questions to ask:
1. Có blocking issues với migration không?
2. Test coverage có đạt target không?
3. Performance benchmarks như thế nào?
4. User feedback gì về new features?
5. Cần support gì cho sprint tiếp theo?

---

## Immediate Next Steps (This Week)

### Day 1-2: Environment Setup
```bash
# Tạo branch mới cho refactor
git checkout -b refactor/phase-0-setup

# Setup development environment
./scripts/setup_dev_environment.sh

# Tạo initial project structure
./scripts/create_project_structure.sh
```

### Day 3-4: First Working Prototype
- Implement DataFetcher base class
- Create simple VNStock adapter
- Write first unit tests
- Setup CI pipeline

### Day 5-7: Validation
- Test adapter với existing data
- Benchmark performance
- Get feedback từ stakeholders
- Plan Phase 1 details 1.1: Base Interfaces và Abstract Classes

**Mục tiêu**: Tạo foundation cho Data Layer

```python
# refactored_src/data_layer/base.py - Tạo file này đầu tiên
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
import pandas as pd

class DataFetcher(ABC):
    @abstractmethod
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        pass
    
    @abstractmethod
    def get_realtime_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        pass

class DataProcessor(ABC):
    @abstractmethod
    def process(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        pass
    
    @abstractmethod
    def validate(self, data: pd.DataFrame) -> bool:
        pass

class DataStorage(ABC):
    @abstractmethod
    def save_data(self, symbol: str, data: pd.DataFrame) -> bool:
        pass
    
    @abstractmethod
    def get_data(self, symbol: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        pass
```

**Timeline**: 2 ngày
**Deliverable**: Base interfaces với unit tests

### Sprint 1.2: VNStock Fetcher Implementation

**Mục tiêu**: Migrate VNStock logic vào architecture mới

```python
# refactored_src/data_layer/fetchers/vnstock_fetcher.py
from typing import Optional
import pandas as pd
from ..base import DataFetcher
from vnstock.explorer.tcbs.quote import Quote

class VNStockFetcher(DataFetcher):
    def __init__(self, rate_limiter=None):
        self.rate_limiter = rate_limiter
        
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        # Migrate existing logic từ get_historical_data
        pass
```

**Timeline**: 3 ngày
**Deliverable**: Working VNStock fetcher với error handling

### Sprint 1.3: Storage Layer với DuckDB

**Timeline**: 3 ngày

## Phase 2: Core Engine Foundation (Tuần 4-5)

### Sprint 2.1: Indicators Module

**Ưu tiên**: Tách VolumeIndicators từ code cũ

```python
# refactored_src/core_engine/indicators/volume.py
class VolumeIndicators:
    def calculate_volume_ratio(self, data: pd.DataFrame, period: int = 10) -> pd.DataFrame:
        # Migrate từ detector.py
        pass
        
    def detect_volume_spike(self, data: pd.DataFrame, threshold: float = 1.5) -> pd.Series:
        # Tách logic từ VolumeBreakoutDetector
        pass
```

### Sprint