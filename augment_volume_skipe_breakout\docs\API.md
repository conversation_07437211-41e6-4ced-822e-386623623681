# Tài liệu API

## <PERSON>ục lục
1. [<PERSON><PERSON><PERSON> dữ liệu](#module-dữ-liệu)
2. [<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> lư<PERSON>](#module-chiến-lượ<PERSON>)
3. [Module backtest](#module-backtest)
4. [Modu<PERSON> chỉ báo](#module-chỉ-báo)
5. [Module giao dịch](#module-giao-dịch)

## Module dữ liệu

### DataFetcher

#### VNStockFetcher
```python
from src.data.fetcher import VNStockFetcher

# Khởi tạo
fetcher = VNStockFetcher()

# Lấy dữ liệu lịch sử
data = fetcher.fetch(symbol="TCB", start_date="2023-01-01", end_date="2023-12-31")

# Lấy dữ liệu hàng loạt
data_batch = fetcher.fetch_batch(
    symbols=["TCB", "VCB", "MBB"], 
    start_date="2023-01-01", 
    end_date="2023-12-31"
)
```

### DataProcessor

#### OHLCVProcessor
```python
from src.data.processor import OHLCVProcessor

# Khởi tạo
processor = OHLCVProcessor()

# Xử lý dữ liệu
processed_data = processor.process(data)
```

### DataStorage

#### CSVStorage
```python
from src.data.storage import CSVStorage

# Khởi tạo
storage = CSVStorage()

# Lưu dữ liệu
storage.save(data, "data/processed/tcb_data.csv")

# Đọc dữ liệu
data = storage.load("data/processed/tcb_data.csv")
```

#### CacheManager
```python
from src.data.storage import CacheManager

# Khởi tạo
cache = CacheManager("data/cache")

# Lưu vào cache
cache.set("tcb_data_2023", data)

# Lấy từ cache
data = cache.get("tcb_data_2023")

# Xóa cache
cache.invalidate("tcb_data_2023")  # Xóa một mục cụ thể
cache.invalidate()  # Xóa tất cả cache
```

## Module chiến lược

### Strategy
```python
from src.strategies.base import Strategy

# Tạo lớp chiến lược tùy chỉnh
class MyStrategy(Strategy):
    def __init__(self, params=None):
        default_params = {
            'param1': 10,
            'param2': 20
        }
        if params:
            default_params.update(params)
        super().__init__("My Strategy", default_params)
        
    def generate_signals(self, data):
        # Tạo tín hiệu giao dịch
        data = data.copy()
        data['signal'] = 0
        
        # Điều kiện mua
        buy_condition = (data['close'] > data['close'].shift(1)) & (data['volume'] > data['volume'].shift(1))
        data.loc[buy_condition, 'signal'] = 1
        
        # Điều kiện bán
        sell_condition = (data['close'] < data['close'].shift(1)) & (data['volume'] > data['volume'].shift(1))
        data.loc[sell_condition, 'signal'] = -1
        
        return data
```

### VolumeBreakoutStrategy
```python
from src.strategies.volume_breakout import VolumeBreakoutStrategy

# Khởi tạo với tham số mặc định
strategy = VolumeBreakoutStrategy()

# Khởi tạo với tham số tùy chỉnh
strategy = VolumeBreakoutStrategy({
    'volume_ma_period': 15,
    'volume_threshold': 2.0,
    'consolidation_days': 15,
    'max_range_percent': 5.0,
    'min_score': 8.0
})

# Tạo tín hiệu
signals = strategy.generate_signals(data)
```

### StrategyFactory
```python
from src.strategies import StrategyFactory

# Liệt kê các chiến lược có sẵn
strategies = StrategyFactory.list_strategies()
print(strategies)  # ['VolumeBreakoutStrategy', 'MovingAverageCrossStrategy', ...]

# Tạo chiến lược từ tên
strategy = StrategyFactory.create('VolumeBreakoutStrategy', volume_threshold=2.0, min_score=8.0)
```

## Module backtest

### BacktestEngine
```python
from src.backtest.engine import BacktestEngine
from src.strategies.volume_breakout import VolumeBreakoutStrategy

# Khởi tạo chiến lược
strategy = VolumeBreakoutStrategy()

# Khởi tạo engine
engine = BacktestEngine(strategy, initial_capital=100000000)

# Chạy backtest
results = engine.run(data)

# Lấy kết quả
print(f"Tổng lợi nhuận: {results['total_return']:.2f}%")
print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
print(f"Drawdown tối đa: {results['max_drawdown']:.2f}%")
```

### Metrics
```python
from src.backtest.metrics import calculate_sharpe_ratio, calculate_drawdown, calculate_win_rate

# Tính tỷ lệ Sharpe
sharpe = calculate_sharpe_ratio(returns, risk_free_rate=0.03)

# Tính drawdown
drawdown, max_drawdown = calculate_drawdown(equity_curve)

# Tính tỷ lệ thắng
win_rate = calculate_win_rate(trades)
```

### Visualizer
```python
from src.backtest.visualizer import plot_equity_curve, plot_drawdown, plot_returns_distribution

# Vẽ đường vốn
plot_equity_curve(results)

# Vẽ drawdown
plot_drawdown(results)

# Vẽ phân phối lợi nhuận
plot_returns_distribution(results)
```

## Module chỉ báo

### Momentum
```python
from src.indicators.momentum import calculate_rsi, calculate_macd

# Tính RSI
data = calculate_rsi(data, period=14)

# Tính MACD
data = calculate_macd(data, fast_period=12, slow_period=26, signal_period=9)
```

### Volume
```python
from src.indicators.volume import calculate_volume_ratio, calculate_obv

# Tính tỷ lệ khối lượng
data = calculate_volume_ratio(data, period=10)

# Tính On-Balance Volume
data = calculate_obv(data)
```

### Trend
```python
from src.indicators.trend import detect_consolidation, calculate_adx

# Phát hiện tích lũy
data = detect_consolidation(data, period=10, threshold=0.07)

# Tính ADX
data = calculate_adx(data, period=14)
```

## Module giao dịch

### Broker
```python
from src.trading.broker import Broker

# Khởi tạo
broker = Broker(api_key="your_api_key", api_secret="your_api_secret")

# Lấy số dư tài khoản
balance = broker.get_balance()

# Lấy danh sách vị thế
positions = broker.get_positions()
```

### Order
```python
from src.trading.order import Order, OrderType, OrderSide

# Tạo lệnh mua
buy_order = Order(
    symbol="TCB",
    side=OrderSide.BUY,
    type=OrderType.LIMIT,
    quantity=1000,
    price=35000
)

# Tạo lệnh bán
sell_order = Order(
    symbol="TCB",
    side=OrderSide.SELL,
    type=OrderType.MARKET,
    quantity=1000
)
```

### Execution
```python
from src.trading.execution import OrderExecutor

# Khởi tạo
executor = OrderExecutor(broker)

# Gửi lệnh
order_id = executor.submit_order(order)

# Hủy lệnh
executor.cancel_order(order_id)

# Lấy trạng thái lệnh
status = executor.get_order_status(order_id)
```
