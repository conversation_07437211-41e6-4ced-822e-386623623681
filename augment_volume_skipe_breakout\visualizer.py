"""
Module hiển thị kết quả phân tích cổ phiếu dưới dạng biểu đồ
"""
import os
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import matplotlib.dates as mdates
from datetime import datetime, timedelta

class StockVisualizer:
    """
    Lớp hiển thị kết quả phân tích cổ phiếu
    """
    
    def __init__(self, output_dir: str = None):
        """
        Khởi tạo StockVisualizer
        
        Args:
            output_dir (str): Thư mục lưu biểu đồ
        """
        self.output_dir = output_dir
        
        # Tạo thư mục output nếu chưa tồn tại
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Thiết lập style cho matplotlib
        plt.style.use('seaborn-v0_8-darkgrid')
            
    def plot_stock(self, symbol: str, data: pd.DataFrame, save: bool = False, show: bool = True):
        """
        Vẽ biểu đồ phân tích cổ phiếu
        
        Args:
            symbol (str): <PERSON><PERSON> cổ phiếu
            data (pd.DataFrame): Dữ liệu lịch sử
            save (bool): Lưu biểu đồ
            show (bool): Hiển thị biểu đồ
        """
        if data is None or data.empty:
            print(f"Không có dữ liệu cho {symbol}")
            return
            
        # Tạo figure với 3 subplots
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1, 1]})
        
        # Đảm bảo dữ liệu được sắp xếp theo thời gian
        data = data.sort_values('time')
        
        # Subplot 1: Biểu đồ giá (candlestick)
        self._plot_candlestick(ax1, data, symbol)
        
        # Subplot 2: Biểu đồ khối lượng
        self._plot_volume(ax2, data)
        
        # Subplot 3: Biểu đồ tỷ lệ khối lượng
        self._plot_volume_ratio(ax3, data)
        
        # Điều chỉnh layout
        plt.tight_layout()
        
        # Lưu biểu đồ
        if save and self.output_dir:
            filename = os.path.join(self.output_dir, f"{symbol}_{datetime.now().strftime('%Y%m%d')}.png")
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"Đã lưu biểu đồ: {filename}")
        
        # Hiển thị biểu đồ
        if show:
            plt.show()
        else:
            plt.close()
            
    def _plot_candlestick(self, ax, data: pd.DataFrame, symbol: str):
        """
        Vẽ biểu đồ nến
        
        Args:
            ax: Matplotlib axis
            data (pd.DataFrame): Dữ liệu lịch sử
            symbol (str): Mã cổ phiếu
        """
        # Chuyển đổi thời gian thành số để vẽ
        x = np.arange(len(data))
        
        # Chiều rộng của nến
        width = 0.6
        
        # Vẽ nến tăng (close > open)
        up = data[data['close'] >= data['open']]
        up_idx = np.array([i for i, d in enumerate(data['time']) if d in up['time'].values])
        
        if len(up_idx) > 0:
            # Thân nến
            ax.bar(
                up_idx, 
                up['close'] - up['open'], 
                width, 
                bottom=up['open'], 
                color='green', 
                alpha=0.7
            )
            
            # Bóng nến
            ax.vlines(
                up_idx, 
                up['low'], 
                up['high'], 
                color='green', 
                linewidth=1
            )
        
        # Vẽ nến giảm (close < open)
        down = data[data['close'] < data['open']]
        down_idx = np.array([i for i, d in enumerate(data['time']) if d in down['time'].values])
        
        if len(down_idx) > 0:
            # Thân nến
            ax.bar(
                down_idx, 
                down['close'] - down['open'], 
                width, 
                bottom=down['open'], 
                color='red', 
                alpha=0.7
            )
            
            # Bóng nến
            ax.vlines(
                down_idx, 
                down['low'], 
                down['high'], 
                color='red', 
                linewidth=1
            )
            
        # Thêm đường MA20
        ma20 = data['close'].rolling(window=20).mean()
        ax.plot(x, ma20, color='blue', linewidth=1.5, label='MA20')
        
        # Thêm đường MA50
        ma50 = data['close'].rolling(window=50).mean()
        ax.plot(x, ma50, color='orange', linewidth=1.5, label='MA50')
        
        # Thiết lập trục x
        dates = [d.strftime('%Y-%m-%d') if isinstance(d, datetime) else d for d in data['time']]
        ax.set_xticks(range(0, len(x), max(1, len(x) // 10)))
        ax.set_xticklabels([dates[i] for i in ax.get_xticks()], rotation=45)
        
        # Thêm tiêu đề và nhãn
        ax.set_title(f"{symbol} - Biểu đồ giá", fontsize=14)
        ax.set_ylabel('Giá (VND)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
    def _plot_volume(self, ax, data: pd.DataFrame):
        """
        Vẽ biểu đồ khối lượng
        
        Args:
            ax: Matplotlib axis
            data (pd.DataFrame): Dữ liệu lịch sử
        """
        # Chuyển đổi thời gian thành số để vẽ
        x = np.arange(len(data))
        
        # Màu cho khối lượng dựa trên giá tăng/giảm
        colors = ['green' if c >= o else 'red' for c, o in zip(data['close'], data['open'])]
        
        # Vẽ biểu đồ khối lượng
        ax.bar(x, data['volume'], color=colors, alpha=0.7)
        
        # Thêm đường trung bình khối lượng 20 phiên
        vol_ma20 = data['volume'].rolling(window=20).mean()
        ax.plot(x, vol_ma20, color='blue', linewidth=1.5, label='Vol MA20')
        
        # Thiết lập trục x
        ax.set_xticks(range(0, len(x), max(1, len(x) // 10)))
        ax.set_xticklabels([])
        
        # Thêm tiêu đề và nhãn
        ax.set_ylabel('Khối lượng (cp)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
    def _plot_volume_ratio(self, ax, data: pd.DataFrame):
        """
        Vẽ biểu đồ tỷ lệ khối lượng
        
        Args:
            ax: Matplotlib axis
            data (pd.DataFrame): Dữ liệu lịch sử
        """
        # Chuyển đổi thời gian thành số để vẽ
        x = np.arange(len(data))
        
        # Tính tỷ lệ khối lượng (so với trung bình 20 phiên)
        vol_ma20 = data['volume'].rolling(window=20).mean()
        vol_ratio = data['volume'] / vol_ma20
        
        # Màu cho tỷ lệ khối lượng
        colors = ['green' if r >= 1 else 'red' for r in vol_ratio]
        
        # Vẽ biểu đồ tỷ lệ khối lượng
        ax.bar(x, vol_ratio, color=colors, alpha=0.7)
        
        # Thêm đường tham chiếu tại 1.0
        ax.axhline(y=1.0, color='blue', linestyle='--', alpha=0.7)
        
        # Thiết lập trục x
        dates = [d.strftime('%Y-%m-%d') if isinstance(d, datetime) else d for d in data['time']]
        ax.set_xticks(range(0, len(x), max(1, len(x) // 10)))
        ax.set_xticklabels([dates[i] for i in ax.get_xticks()], rotation=45)
        
        # Thêm tiêu đề và nhãn
        ax.set_ylabel('Tỷ lệ khối lượng', fontsize=12)
        ax.grid(True, alpha=0.3)
        
    def plot_sector_performance(self, sector_data: Dict[str, float], title: str = "Hiệu suất theo sector"):
        """
        Vẽ biểu đồ hiệu suất theo sector
        
        Args:
            sector_data (Dict[str, float]): Dữ liệu hiệu suất theo sector
            title (str): Tiêu đề biểu đồ
        """
        # Sắp xếp dữ liệu theo hiệu suất
        sorted_data = dict(sorted(sector_data.items(), key=lambda x: x[1], reverse=True))
        
        # Tạo figure
        plt.figure(figsize=(12, 8))
        
        # Vẽ biểu đồ cột
        bars = plt.bar(sorted_data.keys(), sorted_data.values(), color='skyblue')
        
        # Thêm giá trị lên các cột
        for bar in bars:
            height = bar.get_height()
            plt.text(
                bar.get_x() + bar.get_width() / 2.,
                height,
                f'{height:.2f}%',
                ha='center', 
                va='bottom',
                fontsize=10
            )
        
        # Thêm tiêu đề và nhãn
        plt.title(title, fontsize=14)
        plt.ylabel('Hiệu suất (%)', fontsize=12)
        plt.xticks(rotation=45)
        plt.grid(True, axis='y', alpha=0.3)
        
        # Điều chỉnh layout
        plt.tight_layout()
        
        # Hiển thị biểu đồ
        plt.show()
        
    def plot_breakout_distribution(self, result: Dict, title: str = "Phân bố cổ phiếu breakout theo sector"):
        """
        Vẽ biểu đồ phân bố cổ phiếu breakout theo sector
        
        Args:
            result (Dict): Kết quả phân tích
            title (str): Tiêu đề biểu đồ
        """
        # Tạo figure
        plt.figure(figsize=(12, 8))
        
        # Tính số lượng cổ phiếu breakout, theo dõi và không đạt
        breakout_count = len(result.get('breakout', {}))
        watchlist_count = len(result.get('watchlist', {}))
        rejected_count = len(result.get('rejected', {}))
        
        # Vẽ biểu đồ tròn
        labels = ['Breakout', 'Theo dõi', 'Không đạt']
        sizes = [breakout_count, watchlist_count, rejected_count]
        colors = ['green', 'orange', 'red']
        explode = (0.1, 0, 0)  # Nhấn mạnh phần breakout
        
        plt.pie(
            sizes, 
            explode=explode, 
            labels=labels, 
            colors=colors,
            autopct='%1.1f%%',
            shadow=True, 
            startangle=90
        )
        
        # Thêm tiêu đề
        plt.title(title, fontsize=14)
        
        # Đảm bảo biểu đồ tròn
        plt.axis('equal')
        
        # Hiển thị biểu đồ
        plt.show()
        
    def plot_multi_stocks(self, stock_data: Dict[str, pd.DataFrame], title: str = "So sánh cổ phiếu"):
        """
        Vẽ biểu đồ so sánh nhiều cổ phiếu
        
        Args:
            stock_data (Dict[str, pd.DataFrame]): Dữ liệu nhiều cổ phiếu
            title (str): Tiêu đề biểu đồ
        """
        # Tạo figure
        plt.figure(figsize=(12, 8))
        
        # Chuẩn hóa dữ liệu (để so sánh)
        normalized_data = {}
        
        for symbol, data in stock_data.items():
            if data is None or data.empty:
                continue
                
            # Sắp xếp dữ liệu theo thời gian
            data = data.sort_values('time')
            
            # Chuẩn hóa giá (phần trăm thay đổi so với ngày đầu tiên)
            first_price = data['close'].iloc[0]
            normalized_data[symbol] = (data['close'] / first_price - 1) * 100
            
            # Vẽ đường giá
            plt.plot(data['time'], normalized_data[symbol], label=symbol, linewidth=2)
        
        # Thêm tiêu đề và nhãn
        plt.title(title, fontsize=14)
        plt.xlabel('Thời gian', fontsize=12)
        plt.ylabel('Thay đổi giá (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Định dạng trục x
        plt.gcf().autofmt_xdate()
        
        # Điều chỉnh layout
        plt.tight_layout()
        
        # Hiển thị biểu đồ
        plt.show()
