# CHANGELOG

Tài liệu này ghi lại tất cả các thay đổi đáng chú ý trong dự án Volume Skipe Breakout.

## [Unreleased]

## [1.1.0] - 2025-04-07

### Cải tiến
- **Giao diện người dùng**
  - Thêm hiệu ứng đếm ngược từ 5 giây xuống 1 giây khi chuyển giữa các sector
  - Thêm hiệu ứng loading động với các ký tự khối (█, ▓, ▒, ░) thay đổi liên tục
  - Hiể<PERSON> thị thông báo "Đang chuẩn bị dữ liệu... X giây" để người dùng biết chính xác còn bao lâu nữa
  - Xóa màn hình đúng thời điểm để thông báo "chuẩn bị xử lý" biến mất khi bắt đầu phân tích sector mới
  - Loại bỏ thông báo [SECTOR] để tránh hiển thị thông tin trùng lặp, chỉ giữ lại thông báo [INFO]

- **Hiển thị tiến trình**
  - Thanh tiến trình tổng thể hiển thị đúng 100% khi hoàn thành tất cả các sector
  - Thanh tiến trình sector hiển thị đúng 100% khi hoàn thành tất cả các mã trong sector
  - Thêm thông báo "Sector X chuẩn bị xử lý..." trong thời gian chờ đợi giữa các sector
  - Sử dụng ký tự khối (█ và ░) thay vì dấu # và - để thanh tiến trình trông đẹp mắt hơn
  - Hiển thị danh sách các sector đã hoàn thành dưới thanh tiến trình tổng thể

### Sửa lỗi
- Sửa lỗi thông báo "chuẩn bị xử lý" vẫn hiển thị khi đã bắt đầu phân tích sector mới

## [1.0.0] - 2025-04-01

### Tính năng
- Phân tích tự động tất cả các sector
- Tính toán điểm số breakout dựa trên nhiều tiêu chí
- Hiển thị kết quả phân tích tổng hợp
- Phân loại cổ phiếu thành các nhóm: breakout, watchlist, rejected
- Phân tích áp lực thị trường: Mua, Bán, Trung tính
