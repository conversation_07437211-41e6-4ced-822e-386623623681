# Kế hoạch triển khai <PERSON><PERSON> đoạn 1: MVP v<PERSON><PERSON> c<PERSON>u trú<PERSON> chuẩn bị cho tương lai

## 1. Tổng quan

### Mục tiêu
Xây dựng phiên bản t<PERSON><PERSON><PERSON> (MVP) của Augment Volume Skipe Breakout với cấu trúc mở rộng được, đả<PERSON> bảo tất cả chức năng hiện tại hoạt động 100% như ban đầu, đồng thời chuẩn bị nền tảng cho việc mở rộng ở Giai đoạn 2.

### Thời gian
**Timeline**: 3 tuần

### Team
- 1 Backend Developer (full-time)
- 1 Tech Lead (part-time, review)

## 2. C<PERSON>u trúc thư mục đề xuất

```
augment_volume_skipe_breakout/
├── src/
│   ├── data/
│   │   ├── __init__.py
│   │   ├── fetcher.py        # Data fetching từ VNStock
│   │   └── processor.py      # Data processing cơ bản
│   ├── strategies/
│   │   ├── __init__.py
│   │   ├── base.py           # Strategy interface/base class
│   │   └── volume_breakout.py # Volume Breakout strategy
│   ├── backtest/
│   │   ├── __init__.py
│   │   └── simple_backtest.py # Backtest đơn giản
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── config.py         # Configuration management
│   │   └── logger.py         # Logging utilities
│   └── cli/
│       ├── __init__.py
│       └── commands.py       # CLI commands
├── config/
│   └── default.yaml          # Default configuration
├── tests/
│   ├── __init__.py
│   ├── test_data.py
│   ├── test_strategies.py
│   └── test_backtest.py
├── main.py                   # Entry point
├── requirements.txt          # Dependencies
└── README.md                 # Documentation
```

## 3. Task Breakdown

### Tuần 1: Setup & Data Layer

#### Day 1-2: Project Setup
- **Task 1.1**: Thiết lập cấu trúc thư mục mới
  - **Deliverable**: Repository với cấu trúc thư mục đã định nghĩa
  - **Estimate**: 0.5 ngày

- **Task 1.2**: Thiết lập môi trường phát triển
  - **Deliverable**: Virtual environment với dependencies cơ bản
  - **Estimate**: 0.5 ngày

- **Task 1.3**: Thiết lập configuration management
  - **Deliverable**: Module utils/config.py và file config/default.yaml
  - **Estimate**: 1 ngày

#### Day 3-5: Data Layer
- **Task 1.4**: Implement data fetcher
  - **Deliverable**: Module data/fetcher.py với khả năng lấy dữ liệu từ VNStock API
  - **Estimate**: 1.5 ngày

- **Task 1.5**: Implement data processor
  - **Deliverable**: Module data/processor.py với khả năng xử lý dữ liệu cơ bản
  - **Estimate**: 1.5 ngày

#### Day 6-7: Strategy Base
- **Task 1.6**: Thiết kế strategy interface/base class
  - **Deliverable**: Module strategies/base.py với interface chuẩn cho các chiến lược
  - **Estimate**: 1 ngày

- **Task 1.7**: Implement logging utilities
  - **Deliverable**: Module utils/logger.py
  - **Estimate**: 1 ngày

### Tuần 2: Volume Breakout Strategy & Backtest

#### Day 1-3: Volume Breakout Strategy
- **Task 2.1**: Refactor Volume Breakout strategy
  - **Deliverable**: Module strategies/volume_breakout.py implement từ base strategy
  - **Estimate**: 3 ngày

#### Day 4-6: Simple Backtest
- **Task 2.2**: Implement simple backtest engine
  - **Deliverable**: Module backtest/simple_backtest.py
  - **Estimate**: 3 ngày

#### Day 7: CLI Basic
- **Task 2.3**: Implement CLI cơ bản
  - **Deliverable**: Module cli/commands.py với các lệnh cơ bản
  - **Estimate**: 1 ngày

### Tuần 3: Testing, Integration & Documentation

#### Day 1-3: Testing
- **Task 3.1**: Viết unit tests cho các module
  - **Deliverable**: Test cases cho data, strategies, và backtest
  - **Estimate**: 3 ngày

#### Day 4-5: Integration
- **Task 3.2**: Tích hợp các module và đảm bảo hoạt động end-to-end
  - **Deliverable**: main.py và workflow hoàn chỉnh
  - **Estimate**: 2 ngày

#### Day 6-7: Documentation & Release
- **Task 3.3**: Viết documentation và chuẩn bị release
  - **Deliverable**: README.md và release v0.1.0
  - **Estimate**: 2 ngày

## 3.4. Phụ thuộc giữa các Task

Để đảm bảo quá trình triển khai suôn sẻ, dưới đây là các phụ thuộc chính giữa các task:

```
Task 1.1 → Task 1.2 → Task 1.3 → Task 1.4, Task 1.7
Task 1.4 → Task 1.5
Task 1.3, Task 1.7 → Task 1.6 → Task 2.1
Task 2.1 → Task 2.2
Task 1.4, Task 1.5, Task 2.1, Task 2.2 → Task 2.3
Task 1.4, Task 1.5, Task 2.1, Task 2.2 → Task 3.1
Task 2.3, Task 3.1 → Task 3.2 → Task 3.3
```

### Sơ đồ Gantt đơn giản

```
Tuần 1:    |--1.1-|-1.2-|----1.3----|
           |          |----1.4----|----1.5----|
           |                      |----1.6----|--1.7--|
Tuần 2:    |----------2.1----------|
           |                       |------2.2------|
           |                                      |-2.3-|
Tuần 3:    |----------3.1----------|
           |                       |----3.2----|
           |                                   |----3.3----|
```

### Phụ thuộc Module

| Module | Phụ thuộc vào |
|--------|---------------|
| `data/fetcher.py` | `utils/logger.py`, `utils/config.py` |
| `data/processor.py` | `utils/logger.py` |
| `strategies/base.py` | Không có |
| `strategies/volume_breakout.py` | `strategies/base.py` |
| `backtest/simple_backtest.py` | `strategies/base.py` |
| `cli/commands.py` | Tất cả các module khác |
| `main.py` | `cli/commands.py` |

## 4. Chi tiết triển khai

### 4.1. Data Layer

#### Data Fetcher
```python
# src/data/fetcher.py
import os
import pandas as pd
import requests
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class DataFetcher:
    """Base class for data fetchers."""
    
    def __init__(self, cache_dir="./cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def fetch(self, symbol, start_date, end_date):
        """Fetch data for a symbol. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement fetch()")

class VNStockFetcher(DataFetcher):
    """Data fetcher for VNStock API."""
    
    def fetch(self, symbol, start_date, end_date):
        """Fetch stock data for a single symbol from VNStock API."""
        cache_file = f"{self.cache_dir}/{symbol}_{start_date}_{end_date}.csv"
        
        # Check cache first
        if os.path.exists(cache_file):
            logger.info(f"Loading {symbol} from cache")
            return pd.read_csv(cache_file, parse_dates=['date'])
        
        logger.info(f"Fetching {symbol} from VNStock API")
        # Fetch from API
        url = f"https://api.vnstock.com.vn/stocks/{symbol}/historical"
        params = {
            "from": start_date,
            "to": end_date
        }
        
        response = requests.get(url, params=params)
        data = response.json()
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        # Cache result
        df.to_csv(cache_file, index=False)
        
        return df
```

#### Data Processor
```python
# src/data/processor.py
import pandas as pd
import numpy as np

class DataProcessor:
    """Process raw data for analysis."""
    
    def __init__(self):
        pass
    
    def process(self, df):
        """Process raw data, calculate basic indicators."""
        # Ensure date is datetime
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        
        # Sort by date
        df = df.sort_values('date')
        
        # Calculate basic indicators
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        return df
```

### 4.2. Strategy Layer

#### Strategy Base
```python
# src/strategies/base.py
from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, Any

class Strategy(ABC):
    """Base class for all trading strategies."""
    
    def __init__(self, name: str, params: Dict[str, Any] = None):
        self.name = name
        self.params = params or {}
    
    @abstractmethod
    def analyze(self, data: pd.DataFrame) -> pd.DataFrame:
        """Analyze data and generate signals.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with signals
        """
        pass
    
    def get_params(self) -> Dict[str, Any]:
        """Get strategy parameters."""
        return self.params
    
    def set_params(self, params: Dict[str, Any]):
        """Set strategy parameters."""
        self.params = params
```

#### Volume Breakout Strategy
```python
# src/strategies/volume_breakout.py
import pandas as pd
import numpy as np
from typing import Dict, Any
from .base import Strategy

class VolumeBreakoutStrategy(Strategy):
    """Volume Breakout strategy implementation."""
    
    def __init__(self, params: Dict[str, Any] = None):
        default_params = {
            'volume_ma_period': 10,
            'volume_threshold': 1.5,
            'consolidation_days': 10,
            'max_range_percent': 7.0
        }
        
        # Override defaults with provided params
        if params:
            default_params.update(params)
            
        super().__init__("VolumeBreakout", default_params)
    
    def analyze(self, data: pd.DataFrame) -> pd.DataFrame:
        """Analyze data for volume breakout patterns."""
        df = data.copy()
        
        # Calculate volume moving average
        df['volume_ma'] = df['volume'].rolling(
            window=self.params['volume_ma_period']).mean()
        
        # Identify volume breakouts
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        df['is_volume_breakout'] = df['volume_ratio'] > self.params['volume_threshold']
        
        # Identify price consolidation
        df['price_range'] = (df['high'].rolling(window=self.params['consolidation_days']).max() - 
                            df['low'].rolling(window=self.params['consolidation_days']).min()) / \
                            df['low'].rolling(window=self.params['consolidation_days']).min() * 100
        df['is_consolidation'] = df['price_range'] < self.params['max_range_percent']
        
        # Identify breakout signals
        df['signal'] = (df['is_volume_breakout'] & 
                       df['is_consolidation'].shift(1) & 
                       (df['close'] > df['close'].shift(1)))
        
        # Get results
        signals = df[df['signal'] == True].copy()
        
        return signals
```

### 4.3. Backtest Layer

```python
# src/backtest/simple_backtest.py
import pandas as pd
import numpy as np
from typing import Dict, Any
from ..strategies.base import Strategy

class SimpleBacktest:
    """Simple backtesting engine."""
    
    def __init__(self, initial_capital=100000.0, commission=0.0015):
        self.initial_capital = initial_capital
        self.commission = commission
    
    def run(self, data: pd.DataFrame, strategy: Strategy) -> Dict[str, Any]:
        """Run backtest for a strategy.
        
        Args:
            data: DataFrame with OHLCV data
            strategy: Strategy instance
            
        Returns:
            Dict with backtest results
        """
        # Get signals from strategy
        signals = strategy.analyze(data)
        
        if signals.empty:
            return {
                'returns': 0,
                'trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'max_drawdown': 0
            }
        
        # Simulate trades
        trades = []
        capital = self.initial_capital
        
        for _, signal in signals.iterrows():
            # Simple buy and hold for 5 days
            entry_date = signal['date']
            entry_price = signal['close']
            
            # Find exit date and price (5 days later or last available)
            exit_idx = data[data['date'] > entry_date].index[min(5, len(data[data['date'] > entry_date])-1)]
            exit_date = data.loc[exit_idx, 'date']
            exit_price = data.loc[exit_idx, 'close']
            
            # Calculate position size (10% of capital)
            position_size = capital * 0.1
            shares = position_size / entry_price
            
            # Calculate profit/loss
            gross_profit = shares * (exit_price - entry_price)
            commission_cost = position_size * self.commission * 2  # Entry and exit
            net_profit = gross_profit - commission_cost
            
            # Update capital
            capital += net_profit
            
            trades.append({
                'entry_date': entry_date,
                'exit_date': exit_date,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'shares': shares,
                'gross_profit': gross_profit,
                'net_profit': net_profit,
                'return': net_profit / position_size
            })
        
        # Calculate performance metrics
        trades_df = pd.DataFrame(trades)
        
        if trades_df.empty:
            return {
                'returns': 0,
                'trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'max_drawdown': 0
            }
        
        total_return = (capital - self.initial_capital) / self.initial_capital
        win_rate = len(trades_df[trades_df['net_profit'] > 0]) / len(trades_df)
        
        winning_trades = trades_df[trades_df['net_profit'] > 0]['net_profit'].sum()
        losing_trades = abs(trades_df[trades_df['net_profit'] < 0]['net_profit'].sum())
        profit_factor = winning_trades / losing_trades if losing_trades != 0 else float('inf')
        
        # Calculate drawdown
        equity_curve = [self.initial_capital]
        for profit in trades_df['net_profit']:
            equity_curve.append(equity_curve[-1] + profit)
        
        equity_curve = np.array(equity_curve)
        max_equity = np.maximum.accumulate(equity_curve)
        drawdown = (max_equity - equity_curve) / max_equity
        max_drawdown = drawdown.max()
        
        return {
            'returns': total_return,
            'trades': len(trades_df),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'trades_detail': trades_df
        }
```

### 4.4. CLI Layer

```python
# src/cli/commands.py
import click
import pandas as pd
from datetime import datetime, timedelta
import logging

from ..data.fetcher import VNStockFetcher
from ..data.processor import DataProcessor
from ..strategies.volume_breakout import VolumeBreakoutStrategy
from ..backtest.simple_backtest import SimpleBacktest
from ..utils.config import load_config

@click.group()
@click.option('--debug/--no-debug', default=False, help='Enable debug logging')
@click.pass_context
def cli(ctx, debug):
    """Augment Volume Skipe Breakout CLI."""
    # Setup logging
    log_level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Load config
    config = load_config()
    
    # Initialize context
    ctx.ensure_object(dict)
    ctx.obj['config'] = config
    ctx.obj['debug'] = debug

@cli.command()
@click.option('--symbols', required=True, help='Comma-separated list of symbols')
@click.option('--days', default=90, help='Number of days to analyze')
@click.option('--volume-threshold', default=1.5, help='Volume threshold for breakout')
@click.pass_context
def scan(ctx, symbols, days, volume_threshold):
    """Scan symbols for volume breakout patterns."""
    config = ctx.obj['config']
    
    # Setup dates
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    # Initialize components
    fetcher = VNStockFetcher(cache_dir=config['data']['cache_dir'])
    processor = DataProcessor()
    
    strategy_params = {
        'volume_threshold': volume_threshold
    }
    strategy_params.update(config['strategies']['volume_breakout'])
    strategy = VolumeBreakoutStrategy(params=strategy_params)
    
    # Process symbols
    symbols_list = symbols.split(',')
    click.echo(f"Scanning {len(symbols_list)} symbols for volume breakouts...")
    
    results = []
    for symbol in symbols_list:
        click.echo(f"Processing {symbol}...")
        try:
            # Fetch and process data
            df = fetcher.fetch(symbol, start_date, end_date)
            df = processor.process(df)
            
            # Analyze with strategy
            signals = strategy.analyze(df)
            
            if not signals.empty:
                for _, row in signals.iterrows():
                    results.append({
                        'symbol': symbol,
                        'date': row['date'],
                        'close': row['close'],
                        'volume': row['volume'],
                        'volume_ratio': row['volume_ratio']
                    })
        except Exception as e:
            click.echo(f"Error processing {symbol}: {e}")
    
    # Display results
    if results:
        click.echo("\nVolume Breakout Signals:")
        click.echo("-----------------------")
        df_results = pd.DataFrame(results)
        click.echo(df_results.sort_values('date', ascending=False))
    else:
        click.echo("\nNo volume breakout signals found.")

@cli.command()
@click.option('--symbol', required=True, help='Symbol to backtest')
@click.option('--days', default=365, help='Number of days to backtest')
@click.option('--capital', default=100000, help='Initial capital')
@click.pass_context
def backtest(ctx, symbol, days, capital):
    """Backtest Volume Breakout strategy on a symbol."""
    config = ctx.obj['config']
    
    # Setup dates
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    # Initialize components
    fetcher = VNStockFetcher(cache_dir=config['data']['cache_dir'])
    processor = DataProcessor()
    strategy = VolumeBreakoutStrategy(params=config['strategies']['volume_breakout'])
    backtest_engine = SimpleBacktest(initial_capital=capital, 
                                    commission=config['backtest']['commission'])
    
    click.echo(f"Backtesting {symbol} for the last {days} days...")
    
    try:
        # Fetch and process data
        df = fetcher.fetch(symbol, start_date, end_date)
        df = processor.process(df)
        
        # Run backtest
        results = backtest_engine.run(df, strategy)
        
        # Display results
        click.echo("\nBacktest Results:")
        click.echo("-----------------")
        click.echo(f"Symbol: {symbol}")
        click.echo(f"Period: {start_date} to {end_date}")
        click.echo(f"Initial Capital: {capital:,.2f}")
        click.echo(f"Final Capital: {capital * (1 + results['returns']):,.2f}")
        click.echo(f"Total Return: {results['returns']*100:.2f}%")
        click.echo(f"Number of Trades: {results['trades']}")
        click.echo(f"Win Rate: {results['win_rate']*100:.2f}%")
        click.echo(f"Profit Factor: {results['profit_factor']:.2f}")
        click.echo(f"Max Drawdown: {results['max_drawdown']*100:.2f}%")
        
        if 'trades_detail' in results and not results['trades_detail'].empty:
            click.echo("\nTrade Details:")
            click.echo(results['trades_detail'][['entry_date', 'exit_date', 'entry_price', 
                                               'exit_price', 'net_profit', 'return']])
    except Exception as e:
        click.echo(f"Error backtesting {symbol}: {e}")

if __name__ == '__main__':
    cli(obj={})
```

### 4.5. Configuration

```yaml
# config/default.yaml
data:
  cache_dir: "./cache"
  expiry_days: 7
  rate_limit_delay: 0.2

strategies:
  volume_breakout:
    volume_ma_period: 10
    volume_threshold: 1.5
    consolidation_days: 10
    max_range_percent: 7.0

backtest:
  commission: 0.0015
  slippage: 0.001
  position_size: 0.1
```

### 4.6. Main Entry Point

```python
# main.py
from src.cli.commands import cli

if __name__ == '__main__':
    cli(obj={})
```

### 4.7. Định dạng dữ liệu trao đổi giữa các module

#### Schema dữ liệu từ VNStock API
```json
[
  {
    "date": "2023-01-01",
    "open": 100.0,
    "high": 105.0,
    "low": 99.0,
    "close": 102.0,
    "volume": 1000000,
    "value": 102000000
  },
  ...
]
```

#### Định dạng dữ liệu sau khi xử lý bởi DataProcessor
```
DataFrame với các cột:
- date (datetime64): Ngày giao dịch
- open (float): Giá mở cửa
- high (float): Giá cao nhất
- low (float): Giá thấp nhất
- close (float): Giá đóng cửa
- volume (int): Khối lượng giao dịch
- value (float): Giá trị giao dịch
- returns (float): Phần trăm thay đổi giá đóng cửa
- log_returns (float): Log returns
```

#### Định dạng dữ liệu tín hiệu từ Strategy
```
DataFrame với các cột từ dữ liệu gốc và thêm:
- volume_ma (float): Moving average của volume
- volume_ratio (float): Tỷ lệ volume/volume_ma
- is_volume_breakout (bool): Có phải volume breakout không
- price_range (float): Phần trăm biên độ giá
- is_consolidation (bool): Có phải giai đoạn tích lũy không
- signal (bool): Tín hiệu giao dịch
```

## 5. Deliverables

1. **Codebase MVP**: Repository với cấu trúc module hóa, chuẩn bị cho việc mở rộng
2. **Documentation**: README.md với hướng dẫn sử dụng và phát triển
3. **Tests**: Unit tests cho các module chính
4. **Release v0.1.0**: Phiên bản MVP đầu tiên

## 6. Rủi ro và Giảm thiểu

| Rủi ro | Mức độ | Giảm thiểu |
|--------|--------|------------|
| API VNStock thay đổi | Trung bình | Thiết kế interface DataFetcher để dễ dàng thay đổi |
| Hiệu suất kém khi xử lý nhiều cổ phiếu | Cao | Implement caching và xử lý bất đồng bộ ở giai đoạn sau |
| Thiếu tính năng so với phiên bản cũ | Thấp | Đảm bảo tất cả chức năng cốt lõi được implement |
| Phụ thuộc giữa các task không rõ ràng | Trung bình | Xác định rõ phụ thuộc và theo dõi tiến độ hàng ngày |
| Tích hợp các module không suôn sẻ | Cao | Thiết kế interface rõ ràng và kiểm thử sớm |

## 7. Kết luận

Kế hoạch triển khai Giai đoạn 1 tập trung vào việc xây dựng MVP với cấu trúc mở rộng được, đảm bảo tất cả chức năng hiện tại hoạt động 100% như ban đầu. Với timeline 3 tuần và team size nhỏ gọn, kế hoạch này tuân thủ các nguyên tắc MVP đồng thời chuẩn bị nền tảng vững chắc cho việc mở rộng ở Giai đoạn 2.

Cấu trúc module hóa và các interface chuẩn sẽ cho phép dễ dàng thêm các tính năng mới trong tương lai như:
- Thêm các chiến lược giao dịch mới
- Nâng cấp backtest engine
- Thêm các nguồn dữ liệu mới
- Phát triển giao diện người dùng nâng cao

Sau khi hoàn thành Giai đoạn 1, team sẽ có một sản phẩm hoạt động đầy đủ để thu thập feedback từ người dùng, đồng thời có nền tảng vững chắc để tiếp tục phát triển theo kế hoạch Giai đoạn 2.

## 8. Chuẩn bị cho Giai đoạn 2

Trong quá trình triển khai Giai đoạn 1, cần chú ý các điểm sau để chuẩn bị cho Giai đoạn 2:

1. **Thiết kế interface mở rộng**: Đảm bảo các interface đủ linh hoạt để thêm tính năng mới
2. **Documentation đầy đủ**: Viết tài liệu chi tiết về cách mở rộng hệ thống
3. **Test coverage cao**: Đảm bảo test coverage đủ để tự tin refactor trong tương lai
4. **Feedback collection**: Chuẩn bị cơ chế thu thập feedback từ người dùng
5. **Technical debt tracking**: Theo dõi các khoản nợ kỹ thuật để xử lý ở Giai đoạn 2
