"""
Script test rate limit cho project volume_skipe_breakout_v2 (patch silver_sponsorship)
<PERSON><PERSON><PERSON> tra số request tối đa tr<PERSON><PERSON>c khi chạm rate limit API TCBS.
"""
import time
import logging
import os
import sys

# <PERSON><PERSON><PERSON> thư mục patch silver_sponsorship lên đầu sys.path
patch_dir = os.path.join(os.path.dirname(__file__), 'libs', 'vnstock-silver')
if patch_dir not in sys.path:
    sys.path.insert(0, patch_dir)
    logging.info(f"Using vnstock patch from {patch_dir}")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

from vnstock.explorer.tcbs.quote import Quote

def test_rate_limit(symbol, max_calls=100, delay=0.05):
    success = 0
    for i in range(1, max_calls + 1):
        try:
            Quote(symbol).history(start='2025-04-01', end='2025-04-07', interval='1D')
            success += 1
            print(f"{i}: OK")
        except Exception as e:
            print(f"Error at call {i}: {e}")
            break
        time.sleep(delay)
    print(f"Completed {success} successful calls")

if __name__ == '__main__':
    print("Bắt đầu test rate limit v2...")
    test_rate_limit('CTG')
