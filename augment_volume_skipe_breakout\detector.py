import pandas as pd
import numpy as np
from dataclasses import dataclass
import logging
from typing import Dict, List, Optional, Tuple
from config import BreakoutConfig

class VolumeBreakoutDetector:
    """Lớp chính để phát hiện tín hiệu Volume Spike Breakout"""
    
    def __init__(self, config: BreakoutConfig = None):
        self.config = config or BreakoutConfig()
        self._setup_logging()
        
    def _setup_logging(self):
        """Thiết lập logging"""
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def detect_volume_spike(self, data: pd.DataFrame) -> pd.DataFrame:
        """<PERSON><PERSON><PERSON> hiện đột biến khối lượng và điều kiện breakout"""
        df = data.copy()
        
        # Tính toán chỉ số khối lượng
        df['volume_ma'] = df['volume'].rolling(window=self.config.volume_ma_period).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        # Tính toán đặc điểm nến
        df['candle_range'] = df['high'] - df['low']
        df['body_range'] = abs(df['close'] - df['open'])
        df['body_ratio'] = df['body_range'] / df['candle_range']
        
        # Xác định đỉnh trước đó
        df['prev_high'] = df['high'].rolling(window=self.config.lookback_period).max().shift(1)
        
        # Phát hiện điều kiện breakout
        df['is_volume_spike'] = df['volume_ratio'] > self.config.volume_threshold
        df['is_strong_candle'] = (df['body_ratio'] > self.config.candle_body_ratio) & (df['close'] > df['open'])
        df['is_breakout'] = (df['close'] > df['prev_high']) & df['is_volume_spike'] & df['is_strong_candle']
        
        return df

    def calculate_volume_profile(self, data: pd.DataFrame) -> Tuple[pd.Series, float]:
        """Tính toán Volume Profile và POC"""
        price_bins = pd.cut(data['close'], bins=self.config.volume_profile_bins)
        volume_profile = data.groupby(price_bins, observed=True)['volume'].sum()
        poc_bin = volume_profile.idxmax()
        poc_price = poc_bin.mid
        
        return volume_profile, poc_price

    def analyze_price_position(self, current_price: float, poc_price: float) -> Dict:
        """Phân tích vị trí giá hiện tại so với POC"""
        distance_from_poc = (current_price - poc_price) / poc_price
        
        result = {
            'khoang_cach_poc': distance_from_poc,
            'vi_tri': 'gần_poc'
        }
        
        if abs(distance_from_poc) > self.config.poc_distance_threshold:
            result['vi_tri'] = 'trên_poc' if distance_from_poc > 0 else 'dưới_poc'
            
        return result

    def generate_checklist(self, data: pd.DataFrame) -> Dict:
        """Tạo checklist 30 giây cho quyết định giao dịch"""
        latest = data.iloc[-1]
        checklist = {
            'volume_tang_dot_bien': bool(latest['is_volume_spike']),
            'nen_manh': bool(latest['is_strong_candle']),
            'xac_nhan_breakout': bool(latest['is_breakout']),
            'tren_dinh_truoc': bool(latest['close'] > latest['prev_high']),
            'ty_le_rr_hop_ly': self._check_risk_reward(data)
        }
        return checklist

    def _check_risk_reward(self, data: pd.DataFrame) -> bool:
        """Kiểm tra tỷ lệ lợi nhuận/rủi ro"""
        latest = data.iloc[-1]
        breakout_level = latest['prev_high']
        stop_loss = latest['close'] * (1 - self.config.stop_loss_percent)
        risk = latest['close'] - stop_loss
        target = latest['close'] + (risk * self.config.risk_reward_ratio)
        
        return (target - latest['close']) / (latest['close'] - stop_loss) >= self.config.risk_reward_ratio

    def get_trading_signals(self, data: pd.DataFrame) -> Dict:
        """Phân tích và trả về tín hiệu giao dịch"""
        # Kiểm tra điều kiện
        latest_price = data['close'].iloc[-1]
        latest_volume = data['volume'].iloc[-1]
        avg_volume = data['volume'].rolling(window=self.config.volume_ma_period).mean().iloc[-1]
        volume_ratio = latest_volume / avg_volume if avg_volume > 0 else 0
        
        # Tính toán POC và khoảng cách
        poc_data = self._analyze_poc(data)
        
        # Tính điểm breakout
        score = self._calculate_breakout_score(data, volume_ratio)
        
        # Tạo kết quả phân tích
        result = {
            'gia': latest_price,
            'ty_le_volume': volume_ratio,
            'phan_tich_poc': poc_data,
            'khuyen_nghi': {
                'hanh_dong': 'MUA' if score >= 7 else 'THEO_DOI' if score >= 5 else 'KHONG_DAT',
                'ly_do': self._get_recommendation_reason(score, volume_ratio),
                'diem_so': score
            }
        }
        
        # Thêm thông tin giao dịch nếu là MUA
        if result['khuyen_nghi']['hanh_dong'] == 'MUA':
            result['khuyen_nghi'].update({
                'gia_vao': latest_price,
                'stop_loss': self._calculate_stop_loss(data),
                'muc_tieu': self._calculate_target(data),
                'ty_le_vi_the': self._calculate_position_size(data)
            })
            
        return result
        
    def _calculate_breakout_score(self, data: pd.DataFrame, volume_ratio: float) -> float:
        """Tính điểm breakout dựa trên các tiêu chí"""
        score = 0
        
        # Điểm khối lượng (0-4 điểm)
        if volume_ratio >= 3.0:
            score += 4
        elif volume_ratio >= 2.0:
            score += 3
        elif volume_ratio >= 1.5:
            score += 2
        else:
            score += 1
            
        # Điểm xu hướng giá (0-3 điểm)
        price_momentum = self._calculate_price_momentum(data)
        if price_momentum >= 0.1:  # Tăng trên 10%
            score += 3
        elif price_momentum >= 0.05:  # Tăng 5-10%
            score += 2
        elif price_momentum >= 0:  # Tăng 0-5%
            score += 1
            
        # Điểm xu hướng khối lượng (0-3 điểm)
        volume_momentum = self._calculate_volume_momentum(data)
        if volume_momentum >= 1.0:  # Tăng gấp đôi
            score += 3
        elif volume_momentum >= 0.5:  # Tăng 50%
            score += 2
        elif volume_momentum >= 0:  # Tăng 0-50%
            score += 1
            
        return score
        
    def _get_recommendation_reason(self, score: float, volume_ratio: float) -> str:
        """Trả về lý do cho khuyến nghị"""
        if score >= 7:
            return "Điểm breakout cao, khối lượng và giá mạnh"
        elif score >= 5:
            return "Có tín hiệu tốt nhưng cần thêm xác nhận"
        else:
            if volume_ratio < self.config.volume_threshold:
                return f"Khối lượng chưa đủ lớn ({volume_ratio:.1f}x < {self.config.volume_threshold:.1f}x)"
            return "Chưa đủ xác nhận về khối lượng hoặc giá yếu"
            
    def _calculate_price_momentum(self, data: pd.DataFrame) -> float:
        """Tính momentum của giá"""
        lookback = 5  # Số phiên để tính momentum
        if len(data) < lookback:
            return 0
            
        current_price = data['close'].iloc[-1]
        prev_price = data['close'].iloc[-lookback]
        return (current_price - prev_price) / prev_price
        
    def _calculate_volume_momentum(self, data: pd.DataFrame) -> float:
        """Tính momentum của khối lượng"""
        lookback = 5  # Số phiên để tính momentum
        if len(data) < lookback:
            return 0
            
        current_volume = data['volume'].iloc[-1]
        prev_volume = data['volume'].iloc[-lookback:].mean()
        return (current_volume - prev_volume) / prev_volume if prev_volume > 0 else 0

    def _analyze_poc(self, data: pd.DataFrame) -> Dict:
        """Phân tích POC"""
        volume_profile, poc_price = self.calculate_volume_profile(data)
        poc_bin = volume_profile.idxmax()
        poc_range = poc_bin.right - poc_bin.left
        
        return {
            'gia_poc': poc_price,
            'khoang_cach_poc': poc_range
        }
        
    def _calculate_stop_loss(self, data: pd.DataFrame) -> float:
        """Tính stop loss"""
        latest_price = data['close'].iloc[-1]
        return latest_price * (1 - self.config.stop_loss_percent)
        
    def _calculate_target(self, data: pd.DataFrame) -> float:
        """Tính mục tiêu"""
        latest_price = data['close'].iloc[-1]
        stop_loss = self._calculate_stop_loss(data)
        risk = latest_price - stop_loss
        return latest_price + (risk * self.config.risk_reward_ratio)
        
    def _calculate_position_size(self, data: pd.DataFrame) -> float:
        """Tính vị thế"""
        latest_price = data['close'].iloc[-1]
        stop_loss = self._calculate_stop_loss(data)
        risk = latest_price - stop_loss
        return self.config.position_size_ratio * risk / latest_price
