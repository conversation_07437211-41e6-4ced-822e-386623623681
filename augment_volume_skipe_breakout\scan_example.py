import logging
import sys
from datetime import datetime, timedelta
from volume_skipe_breakout.stock_screener import StockScreener
from volume_skipe_breakout.config import BreakoutConfig

# Cấu hình logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Set debug level cho stock_screener
logging.getLogger('stock_screener').setLevel(logging.DEBUG)

def get_user_input(prompt: str, default_value: str) -> str:
    """Lấy input từ người dùng với giá trị mặc định"""
    user_input = input(f"{prompt} (Nhấn Enter để sử dụng giá trị mặc định: {default_value}): ").strip()
    return user_input if user_input else default_value

def print_results(result: dict):
    """
    In kết quả phân tích
    
    Args:
        result (dict): <PERSON><PERSON><PERSON> quả phân tích
    """
    print("\n=== KẾT QUẢ PHÂN TÍCH BREAKOUT ===\n")
    
    # In các cổ phiếu có cơ hội breakout
    print(" CƠ HỘI BREAKOUT:")
    if result['breakout']:
        for symbol, data in result['breakout'].items():
            print(f"  {symbol}:")
            print(f"  - Giá: {data['price']:.2f}")
            print(f"  - Tỷ lệ volume: {data['volume_ratio']:.2f}x")
            print(f"  - Điểm số: {data['score']:.1f}/10")
            print()
    else:
        print("  Không có cơ hội breakout nào\n")
        
    # In các cổ phiếu cần theo dõi
    print(" THEO DÕI:")
    if result['watchlist']:
        for symbol, data in result['watchlist'].items():
            print(f"  {symbol}:")
            print(f"  - Giá: {data['price']:.2f}")
            print(f"  - Tỷ lệ volume: {data['volume_ratio']:.2f}x")
            print(f"  - Điểm số: {data['score']:.1f}/10")
            print()
    else:
        print("  Không có mã nào cần theo dõi\n")
        
    # In các cổ phiếu không đạt
    print(" KHÔNG ĐẠT:")
    if result['rejected']:
        for symbol, data in result['rejected'].items():
            print(f"  {symbol}:")
            print(f"  - Giá: {data['price']:.2f}")
            print(f"  - Tỷ lệ volume: {data['volume_ratio']:.2f}x")
            print(f"  - Lý do: {data['reason']}")
            print()
    else:
        print("  Không có mã nào không đạt\n")

def scan_stocks(min_price: float = 10000, min_volume: float = 100000, consolidation_days: int = 10, max_range_percent: float = 7, market_choice: int = 1):
    """
    Quét cổ phiếu theo các tiêu chí
    
    Args:
        min_price (float): Giá tối thiểu
        min_volume (float): Khối lượng tối thiểu
        consolidation_days (int): Số phiên tích lũy
        max_range_percent (float): Biên độ tích lũy tối đa (%)
        market_choice (int): Lựa chọn thị trường
    """
    # Khởi tạo screener
    config = BreakoutConfig(
        min_price=min_price,
        min_volume=min_volume,
        consolidation_days=consolidation_days,
        max_range_percent=max_range_percent
    )
    
    screener = StockScreener(config=config)
    
    # Xác định loại thị trường hoặc sector
    market_types = {
        1: 'all',
        2: 'vn30',
        3: 'hnx30'
    }
    
    sectors = {
        4: 'banking',
        5: 'securities',
        6: 'real_estate',
        7: 'steel',
        8: 'retail',
        9: 'food_beverage',
        10: 'technology',
        11: 'oil_gas',
        12: 'construction',
        13: 'logistics',
        14: 'textile',
        15: 'others'
    }
    
    print("\nĐang quét với các tham số:")
    print(f"- Giá >= {min_price:,} VND")
    print(f"- Khối lượng >= {min_volume:,} cp/phiên")
    print(f"- Tích lũy {consolidation_days} phiên")
    print(f"- Biên độ tích lũy <= {max_range_percent}%")
    
    # Quét cổ phiếu
    if market_choice in market_types:
        market_type = market_types[market_choice]
        print(f"- Thị trường: {market_type.upper()}")
        result = screener.analyze_market_stocks(
            market_type=market_type,
            min_price=min_price,
            min_volume=min_volume,
            consolidation_days=consolidation_days,
            max_range_percent=max_range_percent
        )
    elif market_choice in sectors:
        sector = sectors[market_choice]
        print(f"- Sector: {sector.upper()}")
        print(f"\nĐang quét {sector.upper()}...")
        
        # Sử dụng phương thức get_potential_stocks_by_sector mới
        potential_stocks = screener.get_potential_stocks_by_sector(
            sector=sector,
            min_price=min_price,
            min_volume=min_volume,
            consolidation_days=consolidation_days,
            max_range_percent=max_range_percent
        )
        
        # Đảm bảo TCB có trong danh sách nếu sector là banking
        if sector.lower() == 'banking' and 'TCB' not in potential_stocks:
            logging.info("Thêm TCB vào danh sách tiềm năng trong scan_stocks")
            potential_stocks.append('TCB')
            print("Đã thêm TCB vào danh sách tiềm năng.")
        
        # In danh sách cổ phiếu tiềm năng
        logging.info(f"Danh sách cổ phiếu tiềm năng: {potential_stocks}")
        
        # Phân tích chi tiết các cổ phiếu tiềm năng
        if potential_stocks:
            result = screener.analyze_stocks(
                symbols=potential_stocks,
                lookback_days=20
            )
        else:
            result = {'breakout': {}, 'watchlist': {}, 'rejected': {}}
    else:
        print(f"Lựa chọn không hợp lệ: {market_choice}")
        return
    
    # Hiển thị kết quả
    print_results(result)

def main():
    """Chương trình chính"""
    print("\n(Nhấn Enter để sử dụng giá trị mặc định)\n")
    
    # Lấy tham số từ người dùng
    min_price = float(get_user_input("Giá tối thiểu (VND)", "10000"))
    min_volume = int(get_user_input("Khối lượng tối thiểu (cp/phiên)", "100000"))
    consolidation_days = int(get_user_input("Số phiên tích lũy", "10"))
    consolidation_threshold = float(get_user_input("Biên độ tích lũy (%)", "7"))
    
    # Khởi tạo screener
    config = BreakoutConfig(
        lookback_period=consolidation_days,
        consolidation_threshold=consolidation_threshold/100,
        volume_threshold=1.5,
        volume_ma_period=10
    )
    screener = StockScreener(config=config)
    
    # Lấy danh sách các sector
    sectors = screener.get_all_sectors()
    
    # Chọn thị trường hoặc sector
    print("\nChọn thị trường hoặc sector:")
    print("1. Toàn thị trường (mặc định)")
    print("2. VN30")
    print("3. HNX30")
    print("--- Hoặc chọn sector ---")
    
    for i, sector in enumerate(sectors, start=4):
        print(f"{i}. {sector.capitalize()}")
    
    market_choice = get_user_input("Lựa chọn của bạn", "1")
    
    # Xác định loại thị trường hoặc sector
    market_type = 'all'
    if market_choice == '2':
        market_type = 'vn30'
    elif market_choice == '3':
        market_type = 'hnx30'
    elif market_choice.isdigit() and 4 <= int(market_choice) <= len(sectors) + 3:
        sector_index = int(market_choice) - 4
        market_type = sectors[sector_index]
    
    print("\nĐang quét với các tham số:")
    print(f"- Giá >= {min_price:,.0f} VND")
    print(f"- Khối lượng >= {min_volume:,} cp/phiên")
    print(f"- Tích lũy {consolidation_days} phiên")
    print(f"- Biên độ <= {consolidation_threshold:.1f}%")
    print(f"- Thị trường/Sector: {market_type.upper()}")
    
    print(f"\nĐang quét {market_type.upper()}...")
    
    # Quét thị trường hoặc sector
    potential_stocks = screener.get_market_potential_stocks(
        market_type=market_type,
        min_price=min_price,
        min_volume=min_volume,
        lookback_days=consolidation_days * 2,
        consolidation_days=consolidation_days,
        max_range_percent=consolidation_threshold
    )
    
    if potential_stocks:
        # Phân tích chi tiết các mã tiềm năng
        analysis = screener.analyze_stocks(
            symbols=potential_stocks,
            lookback_days=consolidation_days * 2
        )
        
        # In kết quả
        print_results(analysis)
    else:
        print("\nKhông tìm thấy cổ phiếu tiềm năng nào.")

if __name__ == "__main__":
    main()
