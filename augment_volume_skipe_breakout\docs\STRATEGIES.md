# Chiến lược giao dịch

## <PERSON><PERSON><PERSON> lục
1. [Volume Breakout](#volume-breakout)
2. [Moving Average Cross](#moving-average-cross)
3. [RSI Divergence](#rsi-divergence)
4. [Bollinger Bands](#bollinger-bands)
5. [Hướng dẫn tạo chiến lược mới](#hướng-dẫn-tạo-chiến-lư<PERSON><PERSON>-mới)

## Volume Breakout

### Mô tả
Chiến lược Volume Breakout tìm kiếm các cổ phiếu đang trong giai đoạn tích lũy và có khối lượng đột biến, đây là dấu hiệu của sự bứt phá.

### Nguyên lý
1. **Tích lũy**: Cổ phiếu đang trong giai đoạn tích lũy, biên độ giá hẹp trong một khoảng thời gian.
2. **<PERSON>h<PERSON>i lượng đột biến**: Kh<PERSON>i lượng giao dịch tăng đột biến so với trung bình.
3. **<PERSON><PERSON><PERSON> tăng**: <PERSON><PERSON><PERSON> đ<PERSON> cửa cao hơn giá mở cửa và vượt qua kháng cự của vùng tích lũy.

### Tham số
- `volume_ma_period`: Số phiên để tính trung bình khối lượng (mặc định: 10)
- `volume_threshold`: Ngưỡng khối lượng đột biến (mặc định: 1.5)
- `consolidation_days`: Số phiên tích lũy (mặc định: 10)
- `max_range_percent`: Biên độ tích lũy tối đa (%) (mặc định: 7.0)
- `min_score`: Điểm số tối thiểu để phát tín hiệu (mặc định: 7.0)

### Tín hiệu
- **Mua (1)**: Khi cổ phiếu thoát khỏi vùng tích lũy với khối lượng đột biến.
- **Bán (-1)**: Khi giá giảm dưới mức stop loss hoặc đạt mức take profit.
- **Không hành động (0)**: Khi không có tín hiệu mua hoặc bán.

### Quản lý rủi ro
- **Stop Loss**: 3% dưới giá vào lệnh
- **Take Profit**: 6% trên giá vào lệnh (tỷ lệ lợi nhuận/rủi ro = 2.0)

### Ví dụ
```python
from src.strategies.volume_breakout import VolumeBreakoutStrategy
from src.data.fetcher import VNStockFetcher
from src.backtest.engine import BacktestEngine

# Lấy dữ liệu
fetcher = VNStockFetcher()
data = fetcher.fetch("TCB", "2023-01-01", "2023-12-31")

# Khởi tạo chiến lược
strategy = VolumeBreakoutStrategy({
    'volume_threshold': 2.0,
    'min_score': 8.0
})

# Chạy backtest
engine = BacktestEngine(strategy, initial_capital=100000000)
results = engine.run(data)

# In kết quả
print(f"Tổng lợi nhuận: {results['total_return']:.2f}%")
```

## Moving Average Cross

### Mô tả
Chiến lược Moving Average Cross sử dụng sự giao cắt của hai đường trung bình động để tạo tín hiệu giao dịch.

### Nguyên lý
1. **Golden Cross**: Khi đường MA ngắn hạn cắt lên trên đường MA dài hạn, tạo tín hiệu mua.
2. **Death Cross**: Khi đường MA ngắn hạn cắt xuống dưới đường MA dài hạn, tạo tín hiệu bán.

### Tham số
- `fast_period`: Số phiên cho MA ngắn hạn (mặc định: 20)
- `slow_period`: Số phiên cho MA dài hạn (mặc định: 50)
- `signal_shift`: Số phiên trễ tín hiệu (mặc định: 1)

### Tín hiệu
- **Mua (1)**: Khi đường MA ngắn hạn cắt lên trên đường MA dài hạn.
- **Bán (-1)**: Khi đường MA ngắn hạn cắt xuống dưới đường MA dài hạn.
- **Không hành động (0)**: Khi không có giao cắt.

### Quản lý rủi ro
- **Stop Loss**: 5% dưới giá vào lệnh
- **Trailing Stop**: 10% dưới giá cao nhất kể từ khi vào lệnh

## RSI Divergence

### Mô tả
Chiến lược RSI Divergence tìm kiếm sự phân kỳ giữa giá và chỉ báo RSI để phát hiện điểm đảo chiều tiềm năng.

### Nguyên lý
1. **Phân kỳ dương**: Khi giá tạo đáy thấp hơn nhưng RSI tạo đáy cao hơn, tạo tín hiệu mua.
2. **Phân kỳ âm**: Khi giá tạo đỉnh cao hơn nhưng RSI tạo đỉnh thấp hơn, tạo tín hiệu bán.

### Tham số
- `rsi_period`: Số phiên tính RSI (mặc định: 14)
- `divergence_lookback`: Số phiên để tìm phân kỳ (mặc định: 20)
- `oversold_threshold`: Ngưỡng quá bán (mặc định: 30)
- `overbought_threshold`: Ngưỡng quá mua (mặc định: 70)

### Tín hiệu
- **Mua (1)**: Khi phát hiện phân kỳ dương và RSI dưới ngưỡng quá bán.
- **Bán (-1)**: Khi phát hiện phân kỳ âm và RSI trên ngưỡng quá mua.
- **Không hành động (0)**: Khi không có phân kỳ.

## Bollinger Bands

### Mô tả
Chiến lược Bollinger Bands sử dụng dải Bollinger để xác định vùng quá mua, quá bán và bứt phá.

### Nguyên lý
1. **Bứt phá**: Khi giá vượt lên trên dải trên hoặc xuống dưới dải dưới.
2. **Hồi về trung bình**: Khi giá từ dải ngoài hồi về đường trung bình.

### Tham số
- `bb_period`: Số phiên tính Bollinger Bands (mặc định: 20)
- `bb_std`: Số độ lệch chuẩn (mặc định: 2.0)
- `entry_threshold`: Ngưỡng vào lệnh (mặc định: 0.05)

### Tín hiệu
- **Mua (1)**: Khi giá từ dưới dải dưới hồi về đường trung bình.
- **Bán (-1)**: Khi giá từ trên dải trên hồi về đường trung bình.
- **Không hành động (0)**: Khi giá trong khoảng giữa dải trên và dưới.

## Hướng dẫn tạo chiến lược mới

### Bước 1: Tạo lớp chiến lược mới
Tạo file mới trong thư mục `src/strategies` với tên chiến lược của bạn, ví dụ: `my_strategy.py`:

```python
from .base import Strategy
from ..indicators.momentum import calculate_rsi
from ..indicators.trend import calculate_adx

class MyStrategy(Strategy):
    """
    Mô tả chiến lược của bạn
    
    Tham số:
        - param1: Mô tả param1
        - param2: Mô tả param2
    """
    
    def __init__(self, params=None):
        default_params = {
            'param1': 10,
            'param2': 20
        }
        
        if params:
            default_params.update(params)
            
        super().__init__("My Strategy", default_params)
        
    def generate_signals(self, data):
        """
        Tạo tín hiệu giao dịch từ dữ liệu
        
        Args:
            data (pd.DataFrame): Dữ liệu OHLCV
            
        Returns:
            pd.DataFrame: Dữ liệu với cột tín hiệu
        """
        # Tính toán các chỉ báo cần thiết
        data = calculate_rsi(data, self.params['param1'])
        
        # Tạo tín hiệu
        data['signal'] = 0
        
        # Điều kiện mua
        buy_condition = (data['rsi'] < 30) & (data['close'] > data['open'])
        data.loc[buy_condition, 'signal'] = 1
        
        # Điều kiện bán
        sell_condition = (data['rsi'] > 70) & (data['close'] < data['open'])
        data.loc[sell_condition, 'signal'] = -1
        
        return data
```

### Bước 2: Đăng ký chiến lược với StrategyFactory
Trong file `src/strategies/__init__.py`, thêm:

```python
from .my_strategy import MyStrategy
from .factory import StrategyFactory

# Đăng ký chiến lược
StrategyFactory.register(MyStrategy)
```

### Bước 3: Kiểm thử chiến lược
Tạo file test trong thư mục `tests/strategies`:

```python
import unittest
import pandas as pd
from src.strategies.my_strategy import MyStrategy

class TestMyStrategy(unittest.TestCase):
    def setUp(self):
        # Tạo dữ liệu test
        self.data = pd.DataFrame({
            'open': [100, 101, 102, 103, 104],
            'high': [105, 106, 107, 108, 109],
            'low': [95, 96, 97, 98, 99],
            'close': [102, 103, 101, 104, 105],
            'volume': [1000, 1100, 900, 1200, 1300]
        })
        
    def test_generate_signals(self):
        # Khởi tạo chiến lược
        strategy = MyStrategy()
        
        # Tạo tín hiệu
        signals = strategy.generate_signals(self.data)
        
        # Kiểm tra kết quả
        self.assertIn('signal', signals.columns)
        self.assertEqual(len(signals), len(self.data))
```

### Bước 4: Tài liệu hóa chiến lược
Thêm mô tả chiến lược vào file `docs/STRATEGIES.md`.
