import sys
import os

print("--- Adding path ---")
silver_lib_path = r'D:/_Code-Python/windsurf/volume_skipe_breakout_v2/libs/vnstock-silver/vnstock_data-2.1.0'
if silver_lib_path not in sys.path:
    sys.path.insert(0, silver_lib_path)
    print(f"Added path: {silver_lib_path}")
else:
    print(f"Path already in sys.path: {silver_lib_path}")

print("\n--- Current sys.path ---")
print(sys.path)

print("\n--- Attempting import ---")
try:
    import vnstock_data
    print("\n--- Successfully imported vnstock_data ---")
    print(f"vnstock_data location: {vnstock_data.__file__}") # In ra vị trí file được import
except ImportError as e:
    print(f"\n--- ImportError occurred ---")
    print(e)
except Exception as e:
    print(f"\n--- An unexpected error occurred during import ---")
    print(e)

print("\n--- Test finished ---")
