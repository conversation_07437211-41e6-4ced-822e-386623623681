"""
Chương trình quét volume breakout cho tất cả các sector (Phiên bản không giới hạn API call)
Kết hợp ưu điểm từ scan_example.py và scan_banking.py
"""
import os
import sys
import logging
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime, timedelta
import random
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
from colorama import Fore, Back, Style, init
import re # Import re here as it's used in get_historical_data

# Khởi tạo colorama
init(autoreset=True)

# Cấu hình logging
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def clear_screen():
    """
    Xóa màn hình terminal, tương thích với cả Windows và Unix/Linux/MacOS
    """
    # Kiểm tra hệ điều hành
    if os.name == 'nt':  # Windows
        os.system('cls')
    else:  # Unix/Linux/MacOS
        os.system('clear')

# Import các module cần thiết
from vnstock.explorer.tcbs.quote import Quote
import sys
import os

# Thêm đường dẫn thư mục hiện tại vào sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(os.path.dirname(current_dir))

# Import các module từ thư mục hiện tại
from config import BreakoutConfig
from stock_screener import StockScreener

# Khởi tạo StockScreener

def create_progress_bar(percent, width=50, color=Fore.GREEN):
    """
    Tạo thanh tiến trình với màu sắc

    Args:
        percent (float): Phần trăm hoàn thành (0-100)
        width (int): Độ rộng của thanh tiến trình
        color (str): Màu sắc của thanh tiến trình

    Returns:
        str: Chuỗi thanh tiến trình
    """
    # Đảm bảo percent nằm trong khoảng 0-100
    percent = max(0, min(100, percent))

    # Đảm bảo khi gần hoàn thành sẽ hiển thị đầy đủ
    if percent > 99.0 or percent == 100.0:
        filled_length = width
    else:
        filled_length = int(width * percent / 100)

    bar = color + "[" + "█" * filled_length + "░" * (width - filled_length) + "]" + Style.RESET_ALL
    return bar

screener = StockScreener()

def calculate_rsi(prices, period=14):
    """
    Tính toán chỉ số RSI (Relative Strength Index)

    Args:
        prices (pd.Series): Chuỗi giá đóng cửa
        period (int): Số phiên để tính RSI

    Returns:
        pd.Series: Chuỗi giá trị RSI
    """
    # Tính toán thay đổi giá
    delta = prices.diff()

    # Phân tách thành giá tăng và giá giảm
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    # Tính trung bình
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()

    # Tính RS và RSI
    # Handle potential division by zero if avg_loss is 0
    rs = avg_gain / avg_loss.replace(0, np.nan) # Replace 0 with NaN to avoid division error
    rsi = 100 - (100 / (1 + rs))
    rsi = rsi.fillna(100) # Fill NaN RSI values (where avg_loss was 0) with 100

    return rsi

def detect_accumulation(data: pd.DataFrame, period: int = 10, threshold: float = 0.07) -> Dict:
    """
    Phát hiện giai đoạn tích lũy

    Args:
        data (pd.DataFrame): Dữ liệu lịch sử
        period (int): Số phiên để xác định tích lũy
        threshold (float): Ngưỡng biên độ tích lũy tối đa

    Returns:
        Dict: Thông tin về giai đoạn tích lũy
    """
    # Lấy dữ liệu trong khoảng thời gian xác định
    recent_data = data.head(period)
    if recent_data.empty:
        return {
            'is_consolidating': False, 'range_percent': np.nan, 'price_range': np.nan,
            'avg_volume': np.nan, 'volume_trend': False, 'pressure': "Không xác định"
        }

    # Tính biên độ giá
    price_range = recent_data['high'].max() - recent_data['low'].min()
    avg_price = recent_data['close'].mean()
    range_percent = price_range / avg_price if avg_price > 0 else np.nan

    # Kiểm tra khối lượng
    avg_volume = recent_data['volume'].mean()
    volume_trend = recent_data['volume'].iloc[0] > avg_volume if not recent_data.empty else False

    # Xác định áp lực mua/bán
    pressure = "Trung tính"
    if not recent_data.empty and recent_data['volume'].iloc[0] > avg_volume:
        if recent_data['close'].iloc[0] > recent_data['open'].iloc[0]:
            pressure = "Mua"  # Khối lượng tăng + Giá tăng = Áp lực mua
        else:
            pressure = "Bán"  # Khối lượng tăng + Giá giảm = Áp lực bán

    # Xác định tích lũy
    is_consolidating = range_percent <= threshold if not pd.isna(range_percent) else False

    return {
        'is_consolidating': is_consolidating,
        'range_percent': range_percent,
        'price_range': price_range,
        'avg_volume': avg_volume,
        'volume_trend': volume_trend,
        'pressure': pressure
    }

def calculate_breakout_score(data: pd.DataFrame, volume_ratio: float, accumulation: Dict) -> float:
    """
    Tính điểm số breakout

    Args:
        data (pd.DataFrame): Dữ liệu lịch sử
        volume_ratio (float): Tỷ lệ khối lượng
        accumulation (Dict): Thông tin tích lũy

    Returns:
        float: Điểm số breakout (0-10)
    """
    score = 0.0

    # Điểm từ tỷ lệ khối lượng (0-5 điểm)
    if volume_ratio >= 2.0:
        score += 5.0
    elif volume_ratio >= 1.5:
        score += 4.0
    elif volume_ratio >= 1.2:
        score += 3.0
    elif volume_ratio >= 1.0:
        score += 2.0
    else:
        score += 1.0

    # Điểm từ tích lũy (0-3 điểm)
    if accumulation.get('is_consolidating', False):
        score += 3.0
    elif accumulation.get('range_percent', float('inf')) <= 0.1:
        score += 2.0
    elif accumulation.get('range_percent', float('inf')) <= 0.15:
        score += 1.0

    # Điểm từ xu hướng giá (0-2 điểm)
    if not data.empty and len(data) >= 20: # Ensure enough data for MA20
        latest_close = data['close'].iloc[0]
        ma20 = data['close'].rolling(window=20).mean().iloc[0] # Use iloc[0] for latest MA20

        if not pd.isna(ma20):
            if latest_close > ma20 * 1.05:  # Giá vượt MA20 hơn 5%
                score += 2.0
            elif latest_close > ma20:  # Giá vượt MA20
                score += 1.0

    return min(10.0, score)

def get_historical_data(symbol: str, lookback_days: int = 30) -> Optional[pd.DataFrame]:
    """
    Lấy dữ liệu lịch sử của một mã cổ phiếu (không giới hạn API)

    Args:
        symbol (str): Mã cổ phiếu
        lookback_days (int): Số ngày lấy dữ liệu lịch sử

    Returns:
        pd.DataFrame: Dữ liệu lịch sử hoặc None nếu lỗi
    """
    try:
        # Tính ngày bắt đầu và kết thúc
        end_date = datetime.now().strftime('%Y-%m-%d')
        # Fetch slightly more data initially to ensure enough for lookback after sorting/filtering
        start_date = (datetime.now() - timedelta(days=lookback_days * 2)).strftime('%Y-%m-%d')

        logging.info(f"Lấy dữ liệu {symbol} từ {start_date} đến {end_date}")

        # Lấy dữ liệu từ vnstock - không retry, không sleep
        quote = Quote(symbol=symbol)
        data = quote.history(start=start_date, end=end_date, interval="1D")

        if data is None or data.empty:
            logging.warning(f"Không thể lấy dữ liệu cho {symbol} hoặc dữ liệu trống.")
            return None

        # Sắp xếp và lấy dữ liệu của lookback_days ngày gần nhất
        data = data.sort_values('time', ascending=False).head(lookback_days)

        if data.empty:
             logging.warning(f"Dữ liệu cho {symbol} trống sau khi lọc {lookback_days} ngày.")
             return None

        return data

    except Exception as e:
        logging.error(f"Lỗi khi lấy dữ liệu lịch sử cho {symbol}: {str(e)}")
        return None

def analyze_stock(symbol: str, lookback_days: int = 30) -> Optional[Dict]:
    """
    Phân tích chi tiết một cổ phiếu (không giới hạn API)

    Args:
        symbol (str): Mã cổ phiếu
        lookback_days (int): Số ngày lấy dữ liệu lịch sử

    Returns:
        Dict: Kết quả phân tích chi tiết hoặc None nếu lỗi
    """
    try:
        # Lấy dữ liệu lịch sử (không có manual_mode)
        data = get_historical_data(symbol, lookback_days)

        if data is None or data.empty:
            logging.warning(f"Không có dữ liệu để phân tích cho {symbol}")
            return None

        # Tính các chỉ số kỹ thuật
        # Ensure enough data for rolling calculations
        if len(data) >= 20:
            data['MA20'] = data['close'].rolling(window=20).mean()
        else:
            data['MA20'] = np.nan
        if len(data) >= 14: # RSI period
             data['RSI'] = calculate_rsi(data['close'])
        else:
             data['RSI'] = np.nan
        data['price_range'] = data['high'] - data['low']

        # Phân tích tích lũy
        accumulation = detect_accumulation(data)

        # Tính toán volume breakout
        latest_volume = data['volume'].iloc[0]
        # Ensure there's data for avg_volume calculation
        avg_volume_data = data['volume'].iloc[1:]
        avg_volume = avg_volume_data.mean() if not avg_volume_data.empty else 0
        volume_ratio = latest_volume / avg_volume if avg_volume > 0 else float('inf') # Use inf if avg_volume is 0

        # Xác định breakout
        breakout_score = calculate_breakout_score(data, volume_ratio, accumulation)

        # Lấy giá trị cuối cùng của chỉ số, xử lý NaN
        latest_ma20 = data['MA20'].iloc[0] if 'MA20' in data.columns and not pd.isna(data['MA20'].iloc[0]) else None
        latest_rsi = data['RSI'].iloc[0] if 'RSI' in data.columns and not pd.isna(data['RSI'].iloc[0]) else None
        latest_price_range = data['price_range'].iloc[0] if 'price_range' in data.columns and not pd.isna(data['price_range'].iloc[0]) else None

        # Tạo kết quả chi tiết
        result = {
            'symbol': symbol,
            'price': data['close'].iloc[0],
            'volume': data['volume'].iloc[0],
            'volume_ratio': volume_ratio,
            'accumulation': accumulation,
            'technical_indicators': {
                'MA20': latest_ma20,
                'RSI': latest_rsi,
                'price_range': latest_price_range
            },
            'score': breakout_score
        }

        # Log thông tin
        logging.info(f"{symbol} - Giá: {result['price']:.2f}, Khối lượng: {int(result['volume'])}")
        logging.info(f"{symbol} - Tỷ lệ khối lượng: {volume_ratio:.2f}x")
        logging.info(f"{symbol} - Đang tích lũy: {'Có' if accumulation.get('is_consolidating') else 'Không'}")
        logging.info(f"{symbol} - Khối lượng tăng: {'Có' if accumulation.get('volume_trend') else 'Không'}")

        return result

    except Exception as e:
        logging.error(f"Lỗi khi phân tích {symbol}: {str(e)}")
        return None

def analyze_sector(sector: str) -> Dict:
    """
    Phân tích toàn bộ sector (không giới hạn API)

    Args:
        sector (str): Tên sector

    Returns:
        Dict: Kết quả phân tích
    """
    # Lấy danh sách cổ phiếu trong sector
    symbols = screener.SECTORS.get(sector.lower(), [])

    if not symbols:
        logging.error(f"Không tìm thấy sector: {sector}")
        return {}

    logging.info(f"Đang phân tích sector {sector} với {len(symbols)} mã cổ phiếu")

    result = {
        'breakout': {},
        'watchlist': {},
        'rejected': {}
    }

    for symbol in symbols:
        try:
            # Phân tích cổ phiếu (không có manual_mode)
            stock_result = analyze_stock(symbol, lookback_days=30)

            if stock_result is None:
                continue

            # Phân loại cổ phiếu
            if stock_result['score'] >= 7.0:  # Breakout
                result['breakout'][symbol] = stock_result
                logging.info(f"{symbol} - Thêm vào danh sách breakout với điểm số {stock_result['score']:.1f}/10")
            elif stock_result['score'] >= 5.0:  # Watchlist
                result['watchlist'][symbol] = stock_result
                logging.info(f"{symbol} - Thêm vào danh sách theo dõi với điểm số {stock_result['score']:.1f}/10")
            else:  # Rejected
                result['rejected'][symbol] = stock_result
                logging.info(f"{symbol} - Không đạt yêu cầu với điểm số {stock_result['score']:.1f}/10")

        except Exception as e:
            logging.error(f"Lỗi khi phân tích {symbol}: {str(e)}")

    # In kết quả phân tích
    logging.info(f"Kết quả phân tích sector {sector}: {len(result['breakout'])} breakout, {len(result['watchlist'])} theo dõi, {len(result['rejected'])} không đạt")

    return result

def analyze_vn30() -> Dict:
    """
    Phân tích các cổ phiếu trong VN30 (không giới hạn API)

    Returns:
        Dict: Kết quả phân tích
    """
    # Lấy danh sách cổ phiếu VN30
    symbols = screener.get_vn30_stocks()

    if not symbols:
        logging.error("Không thể lấy danh sách VN30")
        return {}

    logging.info(f"Đang phân tích VN30 với {len(symbols)} mã cổ phiếu")

    result = {
        'breakout': {},
        'watchlist': {},
        'rejected': {}
    }

    for symbol in symbols:
        try:
            # Phân tích cổ phiếu (không có manual_mode)
            stock_result = analyze_stock(symbol, lookback_days=30)

            if stock_result is None:
                continue

            # Phân loại cổ phiếu
            if stock_result['score'] >= 7.0:  # Breakout
                result['breakout'][symbol] = stock_result
                logging.info(f"{symbol} - Thêm vào danh sách breakout với điểm số {stock_result['score']:.1f}/10")
            elif stock_result['score'] >= 5.0:  # Watchlist
                result['watchlist'][symbol] = stock_result
                logging.info(f"{symbol} - Thêm vào danh sách theo dõi với điểm số {stock_result['score']:.1f}/10")
            else:  # Rejected
                result['rejected'][symbol] = stock_result
                logging.info(f"{symbol} - Không đạt yêu cầu với điểm số {stock_result['score']:.1f}/10")

        except Exception as e:
            logging.error(f"Lỗi khi phân tích {symbol}: {str(e)}")

    # In kết quả phân tích
    logging.info(f"Kết quả phân tích VN30: {len(result['breakout'])} breakout, {len(result['watchlist'])} theo dõi, {len(result['rejected'])} không đạt")

    return result

def print_menu():
    """
    In menu chọn sector
    """
    print("\nChọn sector để phân tích:")
    for i, sector in enumerate(list(screener.SECTORS.keys()), 1):
        print(f"{i}. {sector.capitalize()}")
    print(f"{len(screener.SECTORS) + 1}. VN30")
    print("0. Thoát")

def print_results(result: Dict, sector: str):
    """
    In kết quả phân tích

    Args:
        result (Dict): Kết quả phân tích
        sector (str): Tên sector
    """
    if not result or not isinstance(result, dict) or not all(k in result for k in ['breakout', 'watchlist', 'rejected']):
        print(f"\nKhông có kết quả hợp lệ để hiển thị cho sector {sector}.")
        return

    # Tính toán thống kê
    total_stocks = len(result.get('breakout', {})) + len(result.get('watchlist', {})) + len(result.get('rejected', {}))
    breakout_percent = (len(result.get('breakout', {})) / total_stocks * 100) if total_stocks > 0 else 0
    watchlist_percent = (len(result.get('watchlist', {})) / total_stocks * 100) if total_stocks > 0 else 0

    # Đếm áp lực thị trường
    buying_pressure = 0
    selling_pressure = 0
    neutral_pressure = 0

    for category in ['breakout', 'watchlist', 'rejected']:
        for symbol, data in result.get(category, {}).items():
            pressure = data.get('accumulation', {}).get('pressure', "Trung tính")
            if pressure == "Mua":
                buying_pressure += 1
            elif pressure == "Bán":
                selling_pressure += 1
            else:
                neutral_pressure += 1

    market_pressure = "Trung tính"
    if total_stocks > 0: # Avoid division by zero if no stocks analyzed
        if buying_pressure > selling_pressure * 1.5:
            market_pressure = "Mua mạnh"
        elif buying_pressure > selling_pressure:
            market_pressure = "Mua nhẹ"
        elif selling_pressure > buying_pressure * 1.5:
            market_pressure = "Bán mạnh"
        elif selling_pressure > buying_pressure:
            market_pressure = "Bán nhẹ"

    # In header
    print(f"\n{'='*80}") # Adjusted width
    print(f"{'KẾT QUẢ PHÂN TÍCH BREAKOUT SECTOR ' + sector.upper():^80}")
    print(f"{'='*80}\n")

    # In thống kê tổng quan
    print(f"📊 THỐNG KÊ TỔNG QUAN:")
    print(f"  • Tổng số mã phân tích: {total_stocks}")
    print(f"  • Cơ hội breakout: {len(result.get('breakout', {}))} ({breakout_percent:.1f}%)")
    print(f"  • Theo dõi: {len(result.get('watchlist', {}))} ({watchlist_percent:.1f}%)")
    print(f"  • Không đạt: {len(result.get('rejected', {}))}")
    print(f"  • Áp lực thị trường: {market_pressure}")
    print(f"    - Mua: {buying_pressure} ({(buying_pressure/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"    - Bán: {selling_pressure} ({(selling_pressure/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"    - Trung tính: {neutral_pressure} ({(neutral_pressure/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"{'-'*80}\n") # Adjusted width

    # Hiển thị breakout
    print("🚀 CƠ HỘI BREAKOUT:")
    if result.get('breakout'):
        # In header của bảng
        print(f"  {'Mã':^6}{'Giá':^10}{'Volume':^12}{'Điểm số':^10}{'Tích lũy':^10}{'KL tăng':^10}{'Áp lực':^10}")
        print(f"  {'-'*6}{'-'*10}{'-'*12}{'-'*10}{'-'*10}{'-'*10}{'-'*10}")

        # In dữ liệu
        for symbol, data in sorted(result['breakout'].items(), key=lambda x: x[1].get('score', 0), reverse=True):
            price = data.get('price', 0)
            volume_ratio = data.get('volume_ratio', 0)
            score = data.get('score', 0)
            is_consolidating = "Có" if data.get('accumulation', {}).get('is_consolidating', False) else "Không"
            volume_trend = "Có" if data.get('accumulation', {}).get('volume_trend', False) else "Không"
            pressure = data.get('accumulation', {}).get('pressure', "Không xác định")

            print(f"  {symbol:^6}{price:^10,.2f}{volume_ratio:^12,.2f}x{score:^10,.1f}{is_consolidating:^10}{volume_trend:^10}{pressure:^10}")
    else:
        print("  Không có mã nào đạt điều kiện breakout\n")

    # Hiển thị watchlist
    print("\n👀 THEO DÕI:")
    if result.get('watchlist'):
        # In header của bảng
        print(f"  {'Mã':^6}{'Giá':^10}{'Volume':^12}{'Điểm số':^10}{'Tích lũy':^10}{'KL tăng':^10}{'Áp lực':^10}")
        print(f"  {'-'*6}{'-'*10}{'-'*12}{'-'*10}{'-'*10}{'-'*10}{'-'*10}")

        # In dữ liệu
        for symbol, data in sorted(result['watchlist'].items(), key=lambda x: x[1].get('score', 0), reverse=True):
            price = data.get('price', 0)
            volume_ratio = data.get('volume_ratio', 0)
            score = data.get('score', 0)
            is_consolidating = "Có" if data.get('accumulation', {}).get('is_consolidating', False) else "Không"
            volume_trend = "Có" if data.get('accumulation', {}).get('volume_trend', False) else "Không"
            pressure = data.get('accumulation', {}).get('pressure', "Không xác định")

            print(f"  {symbol:^6}{price:^10,.2f}{volume_ratio:^12,.2f}x{score:^10,.1f}{is_consolidating:^10}{volume_trend:^10}{pressure:^10}")
    else:
        print("  Không có mã nào cần theo dõi\n")

    print(f"\n{'='*80}") # Adjusted width
    print(f"{'Ngày phân tích: ' + datetime.now().strftime('%d/%m/%Y %H:%M'):^80}")
    print(f"{'='*80}\n")

    return result

def setup_logging():
    logging.basicConfig(
        level=logging.WARNING,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def analyze_all_sectors():
    """
    Phân tích tất cả các sector tự động (không giới hạn API)
    """
    # Lấy danh sách sector
    sectors = list(screener.SECTORS.keys())
    sectors.append("VN30")  # Thêm VN30 vào danh sách

    total_sectors = len(sectors)
    completed_sectors = []
    all_results = {}

    # Thống kê tổng hợp cho tất cả các sector
    total_breakout = 0
    total_watchlist = 0
    total_rejected = 0
    total_stocks = 0
    buying_pressure_total = 0
    selling_pressure_total = 0
    neutral_pressure_total = 0

    # Hiển thị thanh tiến trình ban đầu
    clear_screen()
    print("\n====== PHÂN TÍCH TỰ ĐỘNG TẤT CẢ CÁC SECTOR (NO LIMIT) ======\n")

    # Thanh tiến trình ban đầu
    progress_bar = create_progress_bar(0, width=50, color=Fore.GREEN)
    print("\n" + Fore.GREEN + "[PROGRESS]" + Style.RESET_ALL + f" Tiến trình tổng thể: 0/{total_sectors} sectors")
    print(f"{progress_bar} {Fore.GREEN}0.0%{Style.RESET_ALL}")
    print("[]")

    start_time_all = time.time() # Start timing for all sectors

    for i, sector in enumerate(sectors):
        start_time_sector = time.time() # Start timing for this sector
        # Xóa màn hình trước khi bắt đầu phân tích sector mới
        clear_screen()
        print("\n====== PHÂN TÍCH TỰ ĐỘNG TẤT CẢ CÁC SECTOR (NO LIMIT) ======\n")

        # Hiển thị thanh tiến trình tổng thể hiện tại
        current_progress = (i / total_sectors) * 100
        current_progress_bar = create_progress_bar(current_progress, width=50, color=Fore.GREEN)
        print("\n" + Fore.GREEN + "[PROGRESS]" + Style.RESET_ALL + f" Tiến trình tổng thể: {i}/{total_sectors} sectors")
        print(f"{current_progress_bar} {Fore.GREEN}{current_progress:.1f}%{Style.RESET_ALL}")

        # Hiển thị danh sách các sector đã hoàn thành
        if completed_sectors:
            completed_sectors_str = " >> ".join([Fore.YELLOW + s + Style.RESET_ALL for s in completed_sectors])
            print(f"[{completed_sectors_str}]")
        else:
            print("[]")

        # Lấy danh sách mã cổ phiếu trong sector
        if sector == "VN30":
            symbols = screener.get_vn30_stocks()
        else:
            symbols = screener.SECTORS.get(sector.lower(), [])

        sector_symbols_count = len(symbols)
        if sector_symbols_count == 0:
             print("\n" + Fore.YELLOW + "[WARNING]" + Style.RESET_ALL + f" Sector {Fore.YELLOW}{sector}{Style.RESET_ALL} không có mã cổ phiếu nào. Bỏ qua.")
             completed_sectors.append(sector) # Mark as completed even if empty
             time.sleep(1) # Brief pause before next
             continue # Skip to next sector

        # Hiển thị thông tin sector đang phân tích
        print("\n" + Fore.CYAN + "[INFO]" + Style.RESET_ALL + f" Đang phân tích sector: {Fore.YELLOW}{sector}{Style.RESET_ALL} ({sector_symbols_count} mã cổ phiếu)")

        # Phân tích từng mã cổ phiếu trong sector
        sector_result = {'breakout': {}, 'watchlist': {}, 'rejected': {}}

        for j, symbol in enumerate(symbols):
            # Cập nhật thanh tiến trình sector
            symbol_progress = ((j + 1) / sector_symbols_count) * 100
            sector_progress_bar = create_progress_bar(symbol_progress, width=50, color=Fore.CYAN)
            print(f"\r{sector_progress_bar} {Fore.CYAN}{symbol_progress:.1f}%{Style.RESET_ALL} - Mã: {symbol} ({j+1}/{sector_symbols_count})", end="")

            try:
                # Phân tích cổ phiếu (không có manual_mode)
                stock_result = analyze_stock(symbol, lookback_days=30)

                if stock_result is None:
                    continue

                # Phân loại cổ phiếu
                if stock_result['score'] >= 7.0:  # Breakout
                    sector_result['breakout'][symbol] = stock_result
                elif stock_result['score'] >= 5.0:  # Watchlist
                    sector_result['watchlist'][symbol] = stock_result
                else:  # Rejected
                    sector_result['rejected'][symbol] = stock_result

            except Exception as e:
                logging.error(f"Lỗi khi phân tích {symbol} trong sector {sector}: {str(e)}")

        print() # Newline after sector progress bar finishes

        # Lưu kết quả của sector
        all_results[sector] = sector_result

        # Cập nhật thống kê tổng hợp
        sector_breakout = len(sector_result.get('breakout', {}))
        sector_watchlist = len(sector_result.get('watchlist', {}))
        sector_rejected = len(sector_result.get('rejected', {}))
        sector_total = sector_breakout + sector_watchlist + sector_rejected

        total_breakout += sector_breakout
        total_watchlist += sector_watchlist
        total_rejected += sector_rejected
        total_stocks += sector_total

        # Đếm áp lực thị trường cho sector này
        for category in ['breakout', 'watchlist', 'rejected']:
            for symbol, data in sector_result.get(category, {}).items():
                pressure = data.get('accumulation', {}).get('pressure', "Trung tính")
                if pressure == "Mua":
                    buying_pressure_total += 1
                elif pressure == "Bán":
                    selling_pressure_total += 1
                else:
                    neutral_pressure_total += 1

        # Thêm sector vào danh sách đã hoàn thành
        completed_sectors.append(sector)
        end_time_sector = time.time()
        print(Fore.GREEN + f"[INFO] Hoàn thành phân tích sector {sector} trong {end_time_sector - start_time_sector:.2f} giây." + Style.RESET_ALL)
        # time.sleep(0.5) # Optional short pause between sectors if needed

    # --- Kết thúc vòng lặp qua các sector ---

    end_time_all = time.time()
    total_duration = end_time_all - start_time_all

    # Hiển thị kết quả cuối cùng
    clear_screen()
    print("\n====== PHÂN TÍCH TỰ ĐỘNG TẤT CẢ CÁC SECTOR (NO LIMIT) ======\n")

    current_progress = 100.0
    current_progress_bar = create_progress_bar(current_progress, width=50, color=Fore.GREEN)
    print("\n" + Fore.GREEN + "[PROGRESS]" + Style.RESET_ALL + f" Tiến trình tổng thể: {total_sectors}/{total_sectors} sectors")
    print(f"{current_progress_bar} {Fore.GREEN}{current_progress:.1f}%{Style.RESET_ALL}")

    # Hiển thị danh sách các sector đã hoàn thành
    completed_sectors_str = " >> ".join([Fore.YELLOW + s + Style.RESET_ALL for s in completed_sectors])
    print(f"[{completed_sectors_str}]")
    print(f"\nTổng thời gian phân tích: {total_duration:.2f} giây.")


    # Xác định áp lực thị trường tổng thể
    market_pressure = "Trung tính"
    if total_stocks > 0: # Avoid division by zero
        if buying_pressure_total > selling_pressure_total * 1.5:
            market_pressure = "Mua mạnh"
        elif buying_pressure_total > selling_pressure_total:
            market_pressure = "Mua nhẹ"
        elif selling_pressure_total > buying_pressure_total * 1.5:
            market_pressure = "Bán mạnh"
        elif selling_pressure_total > buying_pressure_total:
            market_pressure = "Bán nhẹ"

    # In kết quả tổng hợp
    print("\n" + "="*80) # Adjusted width
    print(f"{'KẾT QUẢ PHÂN TÍCH TỔNG HỢP TẤT CẢ CÁC SECTOR (NO LIMIT)':^80}")
    print("=" * 80 + "\n")

    # In thống kê tổng quan
    print(f"📊 THỐNG KÊ TỔNG QUAN:")
    print(f"  • Tổng số mã phân tích: {total_stocks}")
    print(f"  • Cơ hội breakout: {total_breakout} ({(total_breakout/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"  • Theo dõi: {total_watchlist} ({(total_watchlist/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"  • Không đạt: {total_rejected} ({(total_rejected/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"  • Áp lực thị trường: {market_pressure}")
    print(f"    - Mua: {buying_pressure_total} ({(buying_pressure_total/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"    - Bán: {selling_pressure_total} ({(selling_pressure_total/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"    - Trung tính: {neutral_pressure_total} ({(neutral_pressure_total/total_stocks*100) if total_stocks > 0 else 0:.1f}%)")
    print(f"{'-'*80}\n") # Adjusted width

    # In danh sách cơ hội breakout từ tất cả các sector
    print("🚀 CƠ HỘI BREAKOUT TỪ TẤT CẢ CÁC SECTOR:")
    all_breakouts = {}

    # Tổng hợp các mã breakout và thêm thông tin sector
    for sector, result in all_results.items():
        if result and isinstance(result, dict):
             for symbol, data in result.get('breakout', {}).items():
                # Thêm thông tin sector vào data
                data_with_sector = data.copy()
                data_with_sector['sector'] = sector
                all_breakouts[symbol] = data_with_sector

    # Hiển thị danh sách breakout
    if all_breakouts:
        # In header của bảng
        print(f"  {'Mã':^6}{'Sector':^12}{'Giá':^10}{'Volume':^12}{'Điểm số':^10}{'Tích lũy':^10}{'KL tăng':^10}{'Áp lực':^10}")
        print(f"  {'-'*6}{'-'*12}{'-'*10}{'-'*12}{'-'*10}{'-'*10}{'-'*10}{'-'*10}")

        # Sắp xếp theo điểm số giảm dần
        sorted_breakouts = sorted(all_breakouts.items(), key=lambda x: x[1].get('score', 0), reverse=True)

        # In dữ liệu
        for symbol, data in sorted_breakouts:
            sector = data.get('sector', "")
            price = data.get('price', 0)
            volume_ratio = data.get('volume_ratio', 0)
            score = data.get('score', 0)
            is_consolidating = "Có" if data.get('accumulation', {}).get('is_consolidating', False) else "Không"
            volume_trend = "Có" if data.get('accumulation', {}).get('volume_trend', False) else "Không"
            pressure = data.get('accumulation', {}).get('pressure', "Không xác định")

            print(f"  {symbol:^6}{sector:^12}{price:^10,.2f}{volume_ratio:^12,.2f}x{score:^10,.1f}{is_consolidating:^10}{volume_trend:^10}{pressure:^10}")
    else:
        print("  Không có mã nào đạt điều kiện breakout\n")

    # In danh sách theo dõi từ tất cả các sector
    print("\n👀 TOP 20 MÃ THEO DÕI TỪ TẤT CẢ CÁC SECTOR:")
    all_watchlist = {}

    # Tổng hợp các mã theo dõi và thêm thông tin sector
    for sector, result in all_results.items():
         if result and isinstance(result, dict):
            for symbol, data in result.get('watchlist', {}).items():
                # Thêm thông tin sector vào data
                data_with_sector = data.copy()
                data_with_sector['sector'] = sector
                all_watchlist[symbol] = data_with_sector

    # Hiển thị danh sách theo dõi
    if all_watchlist:
        # In header của bảng
        print(f"  {'Mã':^6}{'Sector':^12}{'Giá':^10}{'Volume':^12}{'Điểm số':^10}{'Tích lũy':^10}{'KL tăng':^10}{'Áp lực':^10}")
        print(f"  {'-'*6}{'-'*12}{'-'*10}{'-'*12}{'-'*10}{'-'*10}{'-'*10}{'-'*10}")

        # Sắp xếp theo điểm số giảm dần và chỉ lấy top 20
        sorted_watchlist = sorted(all_watchlist.items(), key=lambda x: x[1].get('score', 0), reverse=True)[:20]

        # In dữ liệu
        for symbol, data in sorted_watchlist:
            sector = data.get('sector', "")
            price = data.get('price', 0)
            volume_ratio = data.get('volume_ratio', 0)
            score = data.get('score', 0)
            is_consolidating = "Có" if data.get('accumulation', {}).get('is_consolidating', False) else "Không"
            volume_trend = "Có" if data.get('accumulation', {}).get('volume_trend', False) else "Không"
            pressure = data.get('accumulation', {}).get('pressure', "Không xác định")

            print(f"  {symbol:^6}{sector:^12}{price:^10,.2f}{volume_ratio:^12,.2f}x{score:^10,.1f}{is_consolidating:^10}{volume_trend:^10}{pressure:^10}")
    else:
        print("  Không có mã nào cần theo dõi\n")

    print(f"\n{'='*80}") # Adjusted width
    print(f"{'Ngày phân tích: ' + datetime.now().strftime('%d/%m/%Y %H:%M'):^80}")
    print(f"{'='*80}\n")

    return all_results

def main():
    """
    Hàm chính của chương trình (không giới hạn API)
    """
    # Thiết lập logging
    setup_logging()

    # Hiển thị thông tin
    print("\n====== PHÂN TÍCH VOLUME BREAKOUT (NO LIMIT) ======\n")

    # Khởi tạo screener
    global screener
    screener = StockScreener()

    # Hiển thị menu chính
    print("1. Phân tích tự động tất cả các sector")
    print("2. Phân tích theo lựa chọn")
    print("0. Thoát")

    choice = input("Chọn chế độ (0/1/2): ")

    try:
        choice = int(choice)

        if choice == 0:
            print("Kết thúc chương trình.")
            return
        elif choice == 1:
            # Phân tích tự động tất cả các sector
            analyze_all_sectors()
        elif choice == 2:
            # Phân tích theo lựa chọn
            # Lấy danh sách sector
            sectors = list(screener.SECTORS.keys())

            while True:
                # Hiển thị menu
                print_menu()

                # Nhận lựa chọn từ người dùng
                choice_sector = input("Chọn sector (0 để thoát): ")

                try:
                    choice_sector = int(choice_sector)

                    # Thoát
                    if choice_sector == 0:
                        print("Kết thúc chương trình.")
                        break

                    # Kiểm tra lựa chọn hợp lệ
                    if choice_sector < 0 or choice_sector > len(sectors) + 1:
                        print("Lựa chọn không hợp lệ. Vui lòng thử lại.")
                        continue

                    # Xác định sector được chọn
                    if choice_sector == len(sectors) + 1:
                        sector = "VN30"
                    else:
                        sector = sectors[choice_sector - 1]

                    # Bỏ qua lựa chọn chế độ manual/auto

                    # Hiển thị thông tin
                    print(f"\n====== PHÂN TÍCH BREAKOUT SECTOR {sector.upper()} (NO LIMIT) ======")

                    # Phân tích sector (không có manual_mode)
                    if sector == "VN30":
                        result = analyze_vn30()
                    else:
                        result = analyze_sector(sector)

                    # In kết quả
                    print_results(result, sector)

                except ValueError:
                    print("Vui lòng nhập một số nguyên.")
                except Exception as e:
                    logging.error(f"Lỗi trong vòng lặp chọn sector: {str(e)}")
        else:
            print("Lựa chọn không hợp lệ.")
    except ValueError:
        print("Vui lòng nhập một số nguyên.")
    except Exception as e:
        logging.error(f"Lỗi chính: {str(e)}")

if __name__ == "__main__":
    main()