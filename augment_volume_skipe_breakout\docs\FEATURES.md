# Tính năng của dự án Algorithmic Trading

Tài liệu này liệt kê các tính năng của dự án theo định dạng:
`[Mã tính năng] [Trạng thái: Production/Build/Plan] [Tên tính năng]`

## Module Data Management

### DataFetcher

`DF001` `[Production]` Lấy dữ liệu lịch sử từ API vnstock
`DF002` `[Production]` Hỗ trợ Quote object để lấy dữ liệu và thông tin công ty
`DF003` `[Production]` Rate limiting để tránh bị giới hạn API
`DF004` `[Build]` Xử lý lỗi kết nối và tự động thử lại
`DF005` `[Plan]` Hỗ trợ nhiều nguồn dữ liệu (Yahoo Finance, v.v.)

### DataProcessor

`DP001` `[Production]` Làm sạch dữ liệu (xử lý null, outlier, điều chỉnh cổ tức)
`DP002` `[Production]` T<PERSON>h toán các chỉ báo kỹ thuật cơ bản (RSI, MACD, Bollinger Bands)
`DP003` `[Build]` Tạo features phức tạp cho phân tích
`DP004` `[Plan]` Chuẩn hóa dữ liệu cho machine learning

### SmartCache

`SC001` `[Production]` Cache dựa trên file JSON với cấu trúc thư mục theo symbol
`SC002` `[Production]` Quản lý metadata với thời gian lấy và hết hạn
`SC003` `[Production]` Tự động làm mới dữ liệu khi hết hạn
`SC004` `[Build]` Quản lý dung lượng và xóa dữ liệu ít sử dụng (LRU)
`SC005` `[Plan]` Nén dữ liệu để tiết kiệm không gian lưu trữ

## Module Trading Strategies

### VolumeBreakoutStrategy

`VB001` `[Production]` Phát hiện tích lũy dựa trên biến động giá
`VB002` `[Production]` Phát hiện đột phá khối lượng
`VB003` `[Production]` Tính điểm đột phá cho mỗi cổ phiếu
`VB004` `[Build]` Bộ lọc nhiễu để giảm false positive
`VB005` `[Plan]` Tự động tối ưu hóa tham số dựa trên dữ liệu lịch sử

### MovingAverageCrossStrategy

`MA001` `[Production]` Phát hiện tín hiệu giao cắt giữa MA ngắn và MA dài
`MA002` `[Build]` Bộ lọc xu hướng chung của thị trường
`MA003` `[Plan]` Xác nhận tín hiệu bằng khối lượng

## Module Backtesting

### BacktestEngine

`BE001` `[Production]` Mô phỏng giao dịch mua/bán dựa trên tín hiệu
`BE002` `[Production]` Tính toán hiệu suất (lợi nhuận, drawdown, Sharpe ratio)
`BE003` `[Production]` Tính toán phí giao dịch trong backtest
`BE004` `[Build]` Mô phỏng trượt giá khi thực hiện lệnh
`BE005` `[Plan]` Walk-forward testing trên nhiều giai đoạn

### PerformanceMetrics

`PM001` `[Production]` Tính toán các chỉ số cơ bản (ROI, max drawdown, win rate)
`PM002` `[Production]` Tính toán các chỉ số nâng cao (Sharpe, Sortino, Calmar ratio)
`PM003` `[Build]` So sánh với benchmark (VN-Index)
`PM004` `[Plan]` Phân tích rủi ro (VaR, CVaR)

## Module Risk Management

### PositionSizer

`PS001` `[Production]` Fixed size - Kích thước vị thế cố định
`PS002` `[Production]` Percent of equity - Kích thước dựa trên % vốn
`PS003` `[Build]` Kelly criterion - Tối ưu hóa kích thước vị thế
`PS004` `[Plan]` Risk parity - Phân bổ rủi ro đồng đều giữa các vị thế

### StopLossManager

`SL001` `[Production]` Fixed stop-loss - Stop-loss cố định theo %
`SL002` `[Build]` Trailing stop - Stop-loss động theo giá
`SL003` `[Plan]` ATR-based stop - Stop-loss dựa trên chỉ báo ATR
`SL004` `[Plan]` Time-based exit - Thoát vị thế sau một khoảng thời gian

## Module Reporting

### ReportGenerator

`RG001` `[Production]` Báo cáo hiệu suất backtest
`RG002` `[Production]` Biểu đồ equity curve (biển động vốn theo thời gian)
`RG003` `[Build]` Phân tích chi tiết từng giao dịch và thống kê
`RG004` `[Plan]` Xuất báo cáo dưới dạng PDF

### Visualizer

`VZ001` `[Production]` Biểu đồ giá với các chỉ báo kỹ thuật
`VZ002` `[Production]` Đánh dấu điểm mua/bán trên biểu đồ
`VZ003` `[Build]` Biểu đồ so sánh hiệu suất với VN-Index
`VZ004` `[Plan]` Heatmap phân tích hiệu suất theo tham số

## Module User Interface

### CommandLineInterface

`UI001` `[Production]` Giao diện quét cổ phiếu theo chiến lược
`UI002` `[Production]` Hiển thị thanh tiến trình khi phân tích
`UI003` `[Build]` Chạy backtest từ dòng lệnh (CLI)
`UI004` `[Plan]` Tùy chỉnh tham số chiến lược từ CLI

## Tính năng đặc biệt cho thị trường Việt Nam

`VN001` `[Production]` Hỗ trợ lấy dữ liệu từ vnstock API
`VN002` `[Production]` Hỗ trợ Quote object của vnstock
`VN003` `[Production]` Xử lý rate limiting cho API vnstock (delay 0.2s)
`VN004` `[Production]` So sánh hiệu suất với VN-Index

## Hướng dẫn cập nhật tài liệu

1. Khi thêm tính năng mới, hãy tạo mã mới theo định dạng: `[Tiền tố module][Số thứ tự 3 chữ số]`
2. Cập nhật trạng thái: `[Production]` - đã hoàn thành, `[Build]` - đang phát triển, `[Plan]` - trong kế hoạch

Cập nhật lần cuối: 07/04/2025
