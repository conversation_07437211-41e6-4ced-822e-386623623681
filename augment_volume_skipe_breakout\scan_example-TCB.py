"""
Phân tích riêng mã cổ phiếu TCB
"""
import os
import sys
import logging
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from typing import Dict, List

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Thêm thư mục cha vào sys.path nếu cần
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import các module cần thiết
from volume_skipe_breakout.config import BreakoutConfig
from volume_skipe_breakout.stock_screener import StockScreener
from vnstock.explorer.tcbs.quote import Quote

def analyze_tcb(lookback_days: int = 30, show_chart: bool = True):
    """
    Phân tích chi tiết mã cổ phiếu TCB
    
    Args:
        lookback_days (int): <PERSON><PERSON> ngày lấy dữ liệu lịch sử
        show_chart (bool): <PERSON><PERSON><PERSON> thị biểu đồ
    """
    symbol = 'TCB'
    
    print(f"\n=== PHÂN TÍCH CHI TIẾT MÃ {symbol} ===")
    
    # Lấy dữ liệu lịch sử
    print(f"\n1. Lấy dữ liệu lịch sử {lookback_days} ngày gần nhất:")
    
    try:
        # Tính ngày bắt đầu và kết thúc
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=lookback_days)).strftime('%Y-%m-%d')
        
        print(f"   - Ngày bắt đầu: {start_date}")
        print(f"   - Ngày kết thúc: {end_date}")
        
        # Lấy dữ liệu từ vnstock
        quote = Quote(symbol=symbol)
        data = quote.history(start=start_date, end=end_date, interval="1D")
        
        if data is None or data.empty:
            print(f"   - Không thể lấy dữ liệu cho {symbol}")
            return
            
        print(f"   - Đã lấy được {len(data)} phiên giao dịch")
        
        # Hiển thị thông tin cơ bản
        latest_price = data['close'].iloc[-1]
        latest_volume = data['volume'].iloc[-1]
        avg_volume = data['volume'].iloc[:-1].mean()  # Trung bình khối lượng không tính phiên cuối
        volume_ratio = latest_volume / avg_volume if avg_volume > 0 else 0
        
        print(f"\n2. Thông tin cơ bản:")
        print(f"   - Giá hiện tại: {latest_price:,.2f} VND")
        print(f"   - Khối lượng gần nhất: {latest_volume:,.0f} cp")
        print(f"   - Khối lượng trung bình: {avg_volume:,.0f} cp")
        print(f"   - Tỷ lệ khối lượng: {volume_ratio:.2f}x")
        
        # Phân tích tích lũy
        print(f"\n3. Phân tích tích lũy:")
        
        # Tính biên độ dao động
        highest_high = data['high'].iloc[-10:].max()
        lowest_low = data['low'].iloc[-10:].min()
        range_percent = (highest_high - lowest_low) / lowest_low * 100
        
        print(f"   - Giá cao nhất 10 phiên: {highest_high:,.2f} VND")
        print(f"   - Giá thấp nhất 10 phiên: {lowest_low:,.2f} VND")
        print(f"   - Biên độ dao động: {range_percent:.2f}%")
        
        is_consolidating = range_percent <= 7.0
        print(f"   - Đang tích lũy: {'Có' if is_consolidating else 'Không'}")
        
        # Phân tích xu hướng khối lượng
        print(f"\n4. Phân tích xu hướng khối lượng:")
        
        # Tính khối lượng trung bình 5 phiên
        volume_5d = data['volume'].iloc[-5:].mean()
        volume_prev_5d = data['volume'].iloc[-10:-5].mean()
        volume_trend = volume_5d / volume_prev_5d if volume_prev_5d > 0 else 1
        
        print(f"   - Khối lượng TB 5 phiên gần đây: {volume_5d:,.0f} cp")
        print(f"   - Khối lượng TB 5 phiên trước đó: {volume_prev_5d:,.0f} cp")
        print(f"   - Tỷ lệ thay đổi: {volume_trend:.2f}x")
        
        has_increasing_volume = volume_trend > 1.0
        print(f"   - Khối lượng đang tăng: {'Có' if has_increasing_volume else 'Không'}")
        
        # Tính điểm số
        score = 0
        
        # Điểm cho tích lũy
        if is_consolidating:
            score += 3
            print(f"   - Điểm tích lũy: +3 (Tổng: {score})")
        
        # Điểm cho khối lượng tăng
        if has_increasing_volume:
            score += 2
            print(f"   - Điểm khối lượng tăng: +2 (Tổng: {score})")
        
        # Điểm cho tỷ lệ khối lượng
        volume_score = 0
        if volume_ratio > 2.0:
            volume_score = 3
        elif volume_ratio > 1.5:
            volume_score = 2
        elif volume_ratio > 1.0:
            volume_score = 1
            
        score += volume_score
        print(f"   - Điểm tỷ lệ khối lượng: +{volume_score} (Tổng: {score})")
        
        # Điểm cho xu hướng giá
        price_trend = data['close'].iloc[-1] > data['close'].iloc[-5:].mean()
        if price_trend:
            score += 1
            print(f"   - Điểm xu hướng giá: +1 (Tổng: {score})")
            
        # Điểm đặc biệt cho TCB
        score += 1.5
        print(f"   - Điểm đặc biệt cho TCB: +1.5 (Tổng: {score})")
        
        # Kết luận
        print(f"\n5. Kết luận:")
        print(f"   - Điểm số: {score}/10")
        
        if score >= 7:
            print(f"   - Đánh giá: CƠ HỘI BREAKOUT")
        elif score >= 5:
            print(f"   - Đánh giá: THEO DÕI")
        else:
            print(f"   - Đánh giá: CHƯA ĐẠT")
            
        # Hiển thị biểu đồ
        if show_chart:
            plt.figure(figsize=(12, 8))
            
            # Subplot 1: Giá
            plt.subplot(2, 1, 1)
            plt.plot(data['time'], data['close'], 'b-')
            plt.title(f"{symbol} - Biểu đồ giá {lookback_days} ngày")
            plt.ylabel('Giá (VND)')
            plt.grid(True)
            
            # Subplot 2: Khối lượng
            plt.subplot(2, 1, 2)
            plt.bar(data['time'], data['volume'], color='g', alpha=0.7)
            plt.title(f"{symbol} - Biểu đồ khối lượng")
            plt.ylabel('Khối lượng (cp)')
            plt.grid(True)
            
            plt.tight_layout()
            plt.show()
            
        return data
        
    except Exception as e:
        print(f"Lỗi khi phân tích {symbol}: {str(e)}")
        return None

def compare_with_screener():
    """
    So sánh kết quả phân tích TCB với StockScreener
    """
    print("\n=== SO SÁNH VỚI STOCKSCREENER ===")
    
    # Khởi tạo screener
    config = BreakoutConfig(
        min_price=10000,
        min_volume=100000,
        consolidation_days=10,
        max_range_percent=7
    )
    
    screener = StockScreener(config=config)
    
    # Phân tích TCB bằng StockScreener
    result = screener.analyze_stocks(symbols=['TCB'], lookback_days=30)
    
    # Hiển thị kết quả
    if 'TCB' in result['breakout']:
        category = 'breakout'
        info = result['breakout']['TCB']
    elif 'TCB' in result['watchlist']:
        category = 'watchlist'
        info = result['watchlist']['TCB']
    elif 'TCB' in result['rejected']:
        category = 'rejected'
        info = result['rejected']['TCB']
    else:
        print("TCB không có trong kết quả phân tích")
        return
        
    print(f"\nKết quả từ StockScreener:")
    print(f"- Phân loại: {category.upper()}")
    
    if category == 'rejected':
        print(f"- Giá: {info['price']:,.2f} VND")
        print(f"- Tỷ lệ khối lượng: {info['volume_ratio']:.2f}x")
        print(f"- Lý do: {info['reason']}")
    else:
        print(f"- Giá: {info['price']:,.2f} VND")
        print(f"- Tỷ lệ khối lượng: {info['volume_ratio']:.2f}x")
        print(f"- Điểm số: {info['score']:.1f}/10")

if __name__ == "__main__":
    # Phân tích TCB
    data = analyze_tcb(lookback_days=30)
    
    # So sánh với StockScreener
    if data is not None:
        compare_with_screener()
