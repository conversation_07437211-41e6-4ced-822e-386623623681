# Hướng dẫn phát triển

## Mục lục
1. [Thiết lập môi trường](#thiết-lập-môi-trường)
2. [Quy tắc code](#quy-tắc-code)
3. [Quy trình phát triển](#quy-trình-phát-triển)
4. [Hướng dẫn đóng góp](#hướng-dẫn-đóng-góp)
5. [Tài liệu tham khảo](#tài-liệu-tham-khảo)

## Thiết lập môi trường

### Y<PERSON>u cầu hệ thống
- Python 3.8+
- pip
- Git

### Cài đặt
1. Clone repository:
   ```bash
   git clone <repository_url>
   cd volume_skipe_breakout_v2
   ```

2. Tạo môi trường ảo:
   ```bash
   python -m venv venv
   ```

3. <PERSON><PERSON><PERSON> hoạt môi trường ảo:
   - Windows:
     ```bash
     venv\Scripts\activate
     ```
   - Linux/MacOS:
     ```bash
     source venv/bin/activate
     ```

4. Cài đặt các gói phụ thuộc:
   ```bash
   pip install -r requirements.txt
   ```

## Quy tắc code

### Quy ước đặt tên
- **Tên lớp**: PascalCase (ví dụ: `VolumeBreakoutStrategy`)
- **Tên hàm/phương thức**: snake_case (ví dụ: `calculate_breakout_score`)
- **Tên biến**: snake_case (ví dụ: `volume_ratio`)
- **Hằng số**: UPPER_CASE (ví dụ: `MAX_RETRY_COUNT`)

### Định dạng code
- Sử dụng 4 khoảng trắng để thụt đầu dòng
- Giới hạn độ dài dòng: 88 ký tự
- Tuân thủ PEP 8

### Docstrings
Sử dụng định dạng Google cho docstrings:

```python
def function_name(param1, param2):
    """Mô tả ngắn gọn về chức năng.
    
    Mô tả chi tiết hơn nếu cần thiết.
    
    Args:
        param1 (type): Mô tả param1.
        param2 (type): Mô tả param2.
        
    Returns:
        type: Mô tả giá trị trả về.
        
    Raises:
        ExceptionType: Mô tả khi nào ngoại lệ được ném ra.
    """
    # Nội dung hàm
```

## Quy trình phát triển

### Quy trình Git
1. Tạo nhánh từ `main` cho mỗi tính năng/sửa lỗi:
   ```bash
   git checkout -b feature/ten-tinh-nang
   ```

2. Commit thường xuyên với thông điệp rõ ràng:
   ```bash
   git commit -m "feat: Thêm chức năng X"
   ```

3. Push nhánh lên remote:
   ```bash
   git push origin feature/ten-tinh-nang
   ```

4. Tạo Pull Request để merge vào `main`

### Quy ước commit
Sử dụng Conventional Commits:
- `feat`: Thêm tính năng mới
- `fix`: Sửa lỗi
- `docs`: Thay đổi tài liệu
- `style`: Thay đổi không ảnh hưởng đến code (định dạng, khoảng trắng)
- `refactor`: Tái cấu trúc code
- `perf`: Cải thiện hiệu suất
- `test`: Thêm hoặc sửa test
- `chore`: Thay đổi quá trình build hoặc công cụ phụ trợ

### Quy trình phát hành
1. Cập nhật số phiên bản trong `setup.py`
2. Cập nhật `CHANGELOG.md`
3. Tạo tag cho phiên bản mới:
   ```bash
   git tag -a v1.0.0 -m "Version 1.0.0"
   git push origin v1.0.0
   ```

## Hướng dẫn đóng góp

### Quy trình đóng góp
1. Fork repository
2. Tạo nhánh tính năng
3. Thực hiện thay đổi
4. Đảm bảo tất cả test đều pass
5. Tạo Pull Request

### Tiêu chuẩn chấp nhận
- Code phải tuân thủ quy tắc code
- Tất cả test phải pass
- Phải có test cho tính năng mới
- Tài liệu phải được cập nhật

## Tài liệu tham khảo
- [Python PEP 8](https://www.python.org/dev/peps/pep-0008/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Semantic Versioning](https://semver.org/)
