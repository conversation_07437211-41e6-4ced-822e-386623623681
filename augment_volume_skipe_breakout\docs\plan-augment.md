# Kế hoạch Refactoring Codebase theo <PERSON>ến trúc <PERSON>

## 1. <PERSON><PERSON> hoạch Refactoring Chi tiết

### Giai đoạn 1: Thi<PERSON><PERSON> lập Cơ sở Hạ tầng
1. **T<PERSON><PERSON> cấu trúc thư mục mới** theo kiến trúc đã đề xuất
2. **Thiết lập môi trường testing** với pytest
3. **Tạo các file cấu hình** và công cụ quản lý cấu hình

### Giai đoạn 2: Xây dựng Data Layer
1. **Tạo module VNDataFetcher** để truy xuất dữ liệu
2. **Xây dựng hệ thống cache** với DuckDB/Parquet
3. **Tạo các interface truy xuất dữ liệu** chuẩn

### Giai đoạn 3: X<PERSON><PERSON> dựng Core Engine
1. **Tái cấu trúc Screener** từ code hiện tại
2. **T<PERSON><PERSON> cấu trúc VolumeSkipeBreakout** từ detector.py
3. **X<PERSON>y dựng Risk Manager** mới
4. **Xây dựng Portfolio Manager** mới
5. **Xây dựng Paper Broker** mới

### Giai đoạn 4: Xây dựng Interfaces
1. **Tạo CLI/API** interface
2. **Tích hợp Grafana** cho visualization
3. **Chuẩn bị kết nối VN Broker API**

### Giai đoạn 5: Tích hợp và Kiểm thử
1. **Tích hợp tất cả các module**
2. **Viết test end-to-end**
3. **Tối ưu hiệu suất**

## 2. Phương pháp Thực hiện cho Từng Bước

### Giai đoạn 1: Thiết lập Cơ sở Hạ tầng

#### Tạo cấu trúc thư mục mới
- **Cách tiếp cận**: Tạo cấu trúc thư mục theo kiến trúc module hóa
- **Công cụ**: Git, VSCode
- **Bước thực hiện**:
  ```bash
  mkdir -p src/{data_layer,core_engine,interfaces,utils}
  mkdir -p src/data_layer/{fetchers,storage}
  mkdir -p src/core_engine/{screener,strategies,risk,portfolio,broker}
  mkdir -p src/interfaces/{cli,api,visualization}
  mkdir -p tests/{data_layer,core_engine,interfaces,integration}
  ```
- **Kết quả mong đợi**: Cấu trúc thư mục phản ánh kiến trúc mới với các module rõ ràng
- **Thời gian ước tính**: 1 ngày

#### Thiết lập môi trường testing
- **Cách tiếp cận**: Sử dụng pytest và tạo fixtures cơ bản
- **Công cụ**: pytest, pytest-cov
- **Bước thực hiện**:
  - Tạo file `conftest.py` với các fixtures chung:
    ```python
    # conftest.py
    import pytest
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta
    
    @pytest.fixture
    def sample_stock_data():
        """Fixture tạo dữ liệu mẫu cho testing."""
        dates = pd.date_range(start='2023-01-01', end='2023-01-31')
        data = {
            'open': np.random.uniform(10, 20, len(dates)),
            'high': np.random.uniform(15, 25, len(dates)),
            'low': np.random.uniform(5, 15, len(dates)),
            'close': np.random.uniform(10, 20, len(dates)),
            'volume': np.random.uniform(100000, 1000000, len(dates))
        }
        df = pd.DataFrame(data, index=dates)
        return df
    
    @pytest.fixture
    def mock_config():
        """Fixture tạo config mẫu cho testing."""
        return {
            'data': {
                'cache_dir': './test_cache',
                'expiry_days': 1
            },
            'strategy': {
                'volume_breakout': {
                    'volume_ma_period': 10,
                    'volume_threshold': 1.5,
                    'consolidation_days': 10,
                    'max_range_percent': 7.0
                }
            }
        }
    ```
  - Tạo các test case mẫu cho từng module
  - Thiết lập CI/CD với GitHub Actions để tự động chạy test
- **Kết quả mong đợi**: Môi trường testing hoạt động với các fixtures cơ bản
- **Thời gian ước tính**: 2 ngày

#### Tạo các file cấu hình
- **Cách tiếp cận**: Sử dụng YAML cho cấu hình, tạo class ConfigManager
- **Công cụ**: PyYAML
- **Bước thực hiện**:
  - Tạo thư mục `config/` với các file cấu hình mẫu:
    ```yaml
    # config/default.yaml
    data:
      cache_dir: "./cache"
      expiry_days: 7
      rate_limit_delay: 0.2
    
    strategies:
      volume_breakout:
        volume_ma_period: 10
        volume_threshold: 1.5
        consolidation_days: 10
        max_range_percent: 7.0
        min_score: 7.0
    
    risk:
      default_stop_loss: 0.03
      default_take_profit: 0.06
      max_position_size: 0.1
    
    broker:
      commission_rate: 0.0015
      slippage: 0.001
    ```
  - Tạo module `src/utils/config.py` để quản lý cấu hình:
    ```python
    # src/utils/config.py
    import os
    import yaml
    from typing import Dict, Any, Optional
    
    class ConfigManager:
        """Quản lý cấu hình ứng dụng."""
        
        def __init__(self, config_path: str = "config/default.yaml"):
            """Khởi tạo ConfigManager.
            
            Args:
                config_path: Đường dẫn đến file cấu hình
            """
            self.config_path = config_path
            self.config = self._load_config()
        
        def _load_config(self) -> Dict[str, Any]:
            """Tải cấu hình từ file YAML.
            
            Returns:
                Dict chứa cấu hình
            """
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Override từ biến môi trường
            self._override_from_env(config)
            
            return config
        
        def _override_from_env(self, config: Dict[str, Any], prefix: str = "APP_") -> None:
            """Override cấu hình từ biến môi trường.
            
            Args:
                config: Dict cấu hình cần override
                prefix: Tiền tố cho biến môi trường
            """
            for key, value in os.environ.items():
                if key.startswith(prefix):
                    # Chuyển APP_DATA_CACHE_DIR thành ['data', 'cache_dir']
                    config_path = key[len(prefix):].lower().split('_')
                    self._set_nested_key(config, config_path, value)
        
        def _set_nested_key(self, config: Dict[str, Any], path: list, value: Any) -> None:
            """Set giá trị cho key lồng nhau trong dict.
            
            Args:
                config: Dict cấu hình
                path: Danh sách các key lồng nhau
                value: Giá trị cần set
            """
            current = config
            for key in path[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # Chuyển đổi kiểu dữ liệu
            try:
                # Thử chuyển thành số
                if '.' in value:
                    current[path[-1]] = float(value)
                else:
                    current[path[-1]] = int(value)
            except ValueError:
                # Nếu không phải số, giữ nguyên string
                current[path[-1]] = value
        
        def get(self, key: str, default: Optional[Any] = None) -> Any:
            """Lấy giá trị cấu hình.
            
            Args:
                key: Key cấu hình, có thể lồng nhau với dấu chấm (data.cache_dir)
                default: Giá trị mặc định nếu key không tồn tại
            
            Returns:
                Giá trị cấu hình
            """
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if k not in value:
                    return default
                value = value[k]
            
            return value
    ```
  - Hỗ trợ override cấu hình qua biến môi trường
- **Kết quả mong đợi**: Hệ thống cấu hình linh hoạt với khả năng override
- **Thời gian ước tính**: 2 ngày

### Giai đoạn 2: Xây dựng Data Layer

#### Tạo module VNDataFetcher
- **Cách tiếp cận**: Tạo interface chung và implement cho vnstock
- **Công cụ**: vnstock, requests, pandas
- **Bước thực hiện**:
  - Tạo abstract class `DataFetcher` trong `src/data_layer/fetchers/base.py`:
    ```python
    # src/data_layer/fetchers/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Dict, Any, Optional
    from datetime import datetime
    
    class DataFetcher(ABC):
        """Base class for all data fetchers."""
        
        @abstractmethod
        def fetch(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
            """Fetch data for a single symbol.
            
            Args:
                symbol: Stock symbol
                start_date: Start date in YYYY-MM-DD format
                end_date: End date in YYYY-MM-DD format
                
            Returns:
                DataFrame with historical data
            """
            pass
        
        @abstractmethod
        def fetch_batch(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
            """Fetch data for multiple symbols.
            
            Args:
                symbols: List of stock symbols
                start_date: Start date in YYYY-MM-DD format
                end_date: End date in YYYY-MM-DD format
                
            Returns:
                Dictionary mapping symbols to their respective DataFrames
            """
            pass
        
        @abstractmethod
        def get_company_info(self, symbol: str) -> Dict[str, Any]:
            """Get company information.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Dictionary with company information
            """
            pass
    ```
  - Implement `VNStockFetcher` trong `src/data_layer/fetchers/vnstock_fetcher.py`:
    ```python
    # src/data_layer/fetchers/vnstock_fetcher.py
    import time
    import pandas as pd
    from typing import List, Dict, Any, Optional
    from datetime import datetime
    
    from vnstock.explorer.tcbs.quote import Quote
    import vnstock
    
    from src.data_layer.fetchers.base import DataFetcher
    from src.utils.config import ConfigManager
    
    class VNStockFetcher(DataFetcher):
        """Data fetcher implementation using vnstock."""
        
        def __init__(self, config: Optional[ConfigManager] = None):
            """Initialize VNStockFetcher.
            
            Args:
                config: ConfigManager instance
            """
            self.config = config or ConfigManager()
            self.rate_limit_delay = self.config.get('data.rate_limit_delay', 0.2)
            self.max_retries = self.config.get('data.max_retries', 3)
        
        def fetch(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
            """Fetch data for a single symbol using vnstock.
            
            Args:
                symbol: Stock symbol
                start_date: Start date in YYYY-MM-DD format
                end_date: End date in YYYY-MM-DD format
                
            Returns:
                DataFrame with historical data
            """
            for attempt in range(self.max_retries):
                try:
                    # Sử dụng Quote object (khuyến nghị)
                    quote = Quote(symbol=symbol)
                    data = quote.history(start=start_date, end=end_date, interval="1D")
                    
                    # Đảm bảo cột ngày là index
                    if 'time' in data.columns:
                        data = data.set_index('time')
                    
                    # Đảm bảo tên cột chuẩn hóa
                    data = data.rename(columns={
                        'open': 'open',
                        'high': 'high',
                        'low': 'low',
                        'close': 'close',
                        'volume': 'volume'
                    })
                    
                    # Rate limiting
                    time.sleep(self.rate_limit_delay)
                    
                    return data
                
                except Exception as e:
                    print(f"Error fetching data for {symbol}: {str(e)}")
                    if attempt < self.max_retries - 1:
                        # Tăng delay lên gấp đôi cho mỗi lần thử lại
                        time.sleep(self.rate_limit_delay * (2 ** attempt))
                    else:
                        # Trả về DataFrame rỗng nếu đã thử hết số lần
                        return pd.DataFrame()
        
        def fetch_batch(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
            """Fetch data for multiple symbols.
            
            Args:
                symbols: List of stock symbols
                start_date: Start date in YYYY-MM-DD format
                end_date: End date in YYYY-MM-DD format
                
            Returns:
                Dictionary mapping symbols to their respective DataFrames
            """
            result = {}
            for symbol in symbols:
                result[symbol] = self.fetch(symbol, start_date, end_date)
            return result
        
        def get_company_info(self, symbol: str) -> Dict[str, Any]:
            """Get company information.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Dictionary with company information
            """
            try:
                # Sử dụng vnstock API để lấy thông tin công ty
                info = vnstock.company_profile(symbol)
                
                # Rate limiting
                time.sleep(self.rate_limit_delay)
                
                if isinstance(info, pd.DataFrame) and not info.empty:
                    return info.iloc[0].to_dict()
                return {}
            
            except Exception as e:
                print(f"Error fetching company info for {symbol}: {str(e)}")
                return {}
    ```
  - Thêm rate limiting và error handling
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Module VNDataFetcher hoạt động ổn định với xử lý lỗi
- **Thời gian ước tính**: 3 ngày

#### Xây dựng hệ thống cache với DuckDB/Parquet
- **Cách tiếp cận**: Tạo DataStorage interface với DuckDB backend
- **Công cụ**: DuckDB, pyarrow, pandas
- **Bước thực hiện**:
  - Tạo abstract class `DataStorage` trong `src/data_layer/storage/base.py`:
    ```python
    # src/data_layer/storage/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Optional, Dict, Any
    from datetime import datetime
    
    class DataStorage(ABC):
        """Base class for all data storage implementations."""
        
        @abstractmethod
        def save(self, symbol: str, data: pd.DataFrame) -> None:
            """Save data for a symbol.
            
            Args:
                symbol: Stock symbol
                data: DataFrame to save
            """
            pass
        
        @abstractmethod
        def load(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> pd.DataFrame:
            """Load data for a symbol.
            
            Args:
                symbol: Stock symbol
                start_date: Optional start date filter
                end_date: Optional end date filter
                
            Returns:
                DataFrame with the requested data
            """
            pass
        
        @abstractmethod
        def exists(self, symbol: str) -> bool:
            """Check if data exists for a symbol.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                True if data exists, False otherwise
            """
            pass
        
        @abstractmethod
        def get_metadata(self, symbol: str) -> Dict[str, Any]:
            """Get metadata for a symbol.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Dictionary with metadata
            """
            pass
        
        @abstractmethod
        def update_metadata(self, symbol: str, metadata: Dict[str, Any]) -> None:
            """Update metadata for a symbol.
            
            Args:
                symbol: Stock symbol
                metadata: Dictionary with metadata
            """
            pass
        
        @abstractmethod
        def is_expired(self, symbol: str, expiry_days: int = 1) -> bool:
            """Check if data for a symbol is expired.
            
            Args:
                symbol: Stock symbol
                expiry_days: Number of days after which data is considered expired
                
            Returns:
                True if data is expired, False otherwise
            """
            pass
    ```
  - Implement `DuckDBStorage` trong `src/data_layer/storage/duckdb_storage.py`:
    ```python
    # src/data_layer/storage/duckdb_storage.py
    import os
    import json
    import pandas as pd
    import duckdb
    import pyarrow as pa
    import pyarrow.parquet as pq
    from typing import List, Optional, Dict, Any
    from datetime import datetime, timedelta
    
    from src.data_layer.storage.base import DataStorage
    from src.utils.config import ConfigManager
    
    class DuckDBStorage(DataStorage):
        """Data storage implementation using DuckDB and Parquet."""
        
        def __init__(self, config: Optional[ConfigManager] = None):
            """Initialize DuckDBStorage.
            
            Args:
                config: ConfigManager instance
            """
            self.config = config or ConfigManager()
            self.cache_dir = self.config.get('data.cache_dir', './cache')
            self.metadata_dir = os.path.join(self.cache_dir, 'metadata')
            
            # Tạo thư mục cache nếu chưa tồn tại
            os.makedirs(self.cache_dir, exist_ok=True)
            os.makedirs(self.metadata_dir, exist_ok=True)
            
            # Kết nối DuckDB
            self.conn = duckdb.connect(database=':memory:')
        
        def _get_parquet_path(self, symbol: str) -> str:
            """Get path to Parquet file for a symbol.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Path to Parquet file
            """
            return os.path.join(self.cache_dir, f"{symbol.upper()}.parquet")
        
        def _get_metadata_path(self, symbol: str) -> str:
            """Get path to metadata file for a symbol.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Path to metadata file
            """
            return os.path.join(self.metadata_dir, f"{symbol.upper()}.json")
        
        def save(self, symbol: str, data: pd.DataFrame) -> None:
            """Save data for a symbol.
            
            Args:
                symbol: Stock symbol
                data: DataFrame to save
            """
            if data.empty:
                return
            
            # Đảm bảo index là datetime
            if not isinstance(data.index, pd.DatetimeIndex):
                data = data.copy()
                data.index = pd.to_datetime(data.index)
            
            # Lưu dữ liệu dưới dạng Parquet
            table = pa.Table.from_pandas(data.reset_index())
            pq.write_table(table, self._get_parquet_path(symbol))
            
            # Cập nhật metadata
            metadata = {
                'symbol': symbol,
                'last_updated': datetime.now().isoformat(),
                'start_date': data.index.min().isoformat(),
                'end_date': data.index.max().isoformat(),
                'rows': len(data)
            }
            self.update_metadata(symbol, metadata)
        
        def load(self, symbol: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> pd.DataFrame:
            """Load data for a symbol.
            
            Args:
                symbol: Stock symbol
                start_date: Optional start date filter
                end_date: Optional end date filter
                
            Returns:
                DataFrame with the requested data
            """
            if not self.exists(symbol):
                return pd.DataFrame()
            
            # Đọc dữ liệu từ Parquet sử dụng DuckDB
            parquet_path = self._get_parquet_path(symbol)
            
            # Tạo query với filter nếu cần
            query = f"SELECT * FROM '{parquet_path}'"
            if start_date or end_date:
                query += " WHERE "
                conditions = []
                if start_date:
                    conditions.append(f"time >= '{start_date}'")
                if end_date:
                    conditions.append(f"time <= '{end_date}'")
                query += " AND ".join(conditions)
            
            # Thực thi query
            result = self.conn.execute(query).fetchdf()
            
            # Đặt cột time làm index
            if 'time' in result.columns:
                result = result.set_index('time')
            
            return result
        
        def exists(self, symbol: str) -> bool:
            """Check if data exists for a symbol.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                True if data exists, False otherwise
            """
            return os.path.exists(self._get_parquet_path(symbol))
        
        def get_metadata(self, symbol: str) -> Dict[str, Any]:
            """Get metadata for a symbol.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Dictionary with metadata
            """
            metadata_path = self._get_metadata_path(symbol)
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    return json.load(f)
            return {}
        
        def update_metadata(self, symbol: str, metadata: Dict[str, Any]) -> None:
            """Update metadata for a symbol.
            
            Args:
                symbol: Stock symbol
                metadata: Dictionary with metadata
            """
            metadata_path = self._get_metadata_path(symbol)
            
            # Merge với metadata hiện tại nếu có
            current_metadata = self.get_metadata(symbol)
            current_metadata.update(metadata)
            
            with open(metadata_path, 'w') as f:
                json.dump(current_metadata, f, indent=2)
        
        def is_expired(self, symbol: str, expiry_days: int = 1) -> bool:
            """Check if data for a symbol is expired.
            
            Args:
                symbol: Stock symbol
                expiry_days: Number of days after which data is considered expired
                
            Returns:
                True if data is expired, False otherwise
            """
            if not self.exists(symbol):
                return True
            
            metadata = self.get_metadata(symbol)
            if 'last_updated' not in metadata:
                return True
            
            last_updated = datetime.fromisoformat(metadata['last_updated'])
            expiry_date = datetime.now() - timedelta(days=expiry_days)
            
            return last_updated < expiry_date
    ```
  - Thêm cơ chế quản lý cache thông minh với metadata
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Hệ thống cache hiệu quả với DuckDB và Parquet
- **Thời gian ước tính**: 4 ngày

#### Tạo các interface truy xuất dữ liệu
- **Cách tiếp cận**: Tạo facade pattern để đơn giản hóa truy xuất dữ liệu
- **Công cụ**: pandas, typing
- **Bước thực hiện**:
  - Tạo class `DataManager` trong `src/data_layer/data_manager.py`:
    ```python
    # src/data_layer/data_manager.py
    import pandas as pd
    from typing import List, Dict, Any, Optional
    from datetime import datetime, timedelta
    
    from src.data_layer.fetchers.base import DataFetcher
    from src.data_layer.fetchers.vnstock_fetcher import VNStockFetcher
    from src.data_layer.storage.base import DataStorage
    from src.data_layer.storage.duckdb_storage import DuckDBStorage
    from src.utils.config import ConfigManager
    
    class DataManager:
        """Facade for data access."""
        
        def __init__(
            self,
            fetcher: Optional[DataFetcher] = None,
            storage: Optional[DataStorage] = None,
            config: Optional[ConfigManager] = None
        ):
            """Initialize DataManager.
            
            Args:
                fetcher: DataFetcher instance
                storage: DataStorage instance
                config: ConfigManager instance
            """
            self.config = config or ConfigManager()
            self.fetcher = fetcher or VNStockFetcher(self.config)
            self.storage = storage or DuckDBStorage(self.config)
            self.expiry_days = self.config.get('data.expiry_days', 1)
        
        def get_data(
            self,
            symbol: str,
            start_date: str,
            end_date: str,
            force_refresh: bool = False
        ) -> pd.DataFrame:
            """Get data for a symbol.
            
            Args:
                symbol: Stock symbol
                start_date: Start date in YYYY-MM-DD format
                end_date: End date in YYYY-MM-DD format
                force_refresh: Force refresh from API
                
            Returns:
                DataFrame with historical data
            """
            # Kiểm tra cache
            if not force_refresh and self.storage.exists(symbol) and not self.storage.is_expired(symbol, self.expiry_days):
                return self.storage.load(symbol, start_date, end_date)
            
            # Lấy dữ liệu từ API
            data = self.fetcher.fetch(symbol, start_date, end_date)
            
            # Lưu vào cache
            if not data.empty:
                self.storage.save(symbol, data)
            
            return data
        
        def get_data_batch(
            self,
            symbols: List[str],
            start_date: str,
            end_date: str,
            force_refresh: bool = False
        ) -> Dict[str, pd.DataFrame]:
            """Get data for multiple symbols.
            
            Args:
                symbols: List of stock symbols
                start_date: Start date in YYYY-MM-DD format
                end_date: End date in YYYY-MM-DD format
                force_refresh: Force refresh from API
                
            Returns:
                Dictionary mapping symbols to their respective DataFrames
            """
            result = {}
            for symbol in symbols:
                result[symbol] = self.get_data(symbol, start_date, end_date, force_refresh)
            return result
        
        def get_company_info(self, symbol: str) -> Dict[str, Any]:
            """Get company information.
            
            Args:
                symbol: Stock symbol
                
            Returns:
                Dictionary with company information
            """
            return self.fetcher.get_company_info(symbol)
    ```
  - Implement các phương thức high-level để truy xuất dữ liệu
  - Tích hợp cả fetcher và storage
- **Kết quả mong đợi**: Interface truy xuất dữ liệu đơn giản và hiệu quả
- **Thời gian ước tính**: 2 ngày

### Giai đoạn 3: Xây dựng Core Engine

#### Tái cấu trúc Screener
- **Cách tiếp cận**: Chuyển đổi code hiện tại sang module mới
- **Công cụ**: pandas, typing
- **Bước thực hiện**:
  - Tạo abstract class `Screener` trong `src/core_engine/screener/base.py`:
    ```python
    # src/core_engine/screener/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Dict, Any, Optional
    
    class Screener(ABC):
        """Base class for all screeners."""
        
        @abstractmethod
        def screen(self, symbols: List[str], **kwargs) -> List[Dict[str, Any]]:
            """Screen symbols based on criteria.
            
            Args:
                symbols: List of stock symbols
                **kwargs: Additional parameters
                
            Returns:
                List of dictionaries with screening results
            """
            pass
        
        @abstractmethod
        def rank(self, results: List[Dict[str, Any]]) -> List
    ```
  - Implement `StockScreener` trong `src/core_engine/screener/stock_screener.py`:
    ```python
    # src/core_engine/screener/stock_screener.py
    import pandas as pd
    from typing import List, Dict, Any
    from datetime import datetime
    
    from src.core_engine.screener.base import Screener
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    class StockScreener(Screener):
        """Screen stocks based on multiple criteria."""
        
        def __init__(self, data_manager: DataManager, config: ConfigManager):
            """Initialize StockScreener.
            
            Args:
                data_manager: DataManager instance
                config: ConfigManager instance
            """
            self.data_manager = data_manager
            self.config = config
            self.screener_config = config.get('screener', {})
        
        def screen(self, symbols: List[str], **kwargs) -> List[Dict[str, Any]]:
            """Screen stocks based on criteria.
            
            Args:
                symbols: List of stock symbols
                **kwargs: Additional parameters
                
            Returns:
                List of dictionaries with screening results
            """
            results = []
            for symbol in symbols:
                try:
                    data = self.data_manager.get_data(symbol, **kwargs)
                    if data.empty:
                        continue
                    
                    # Apply filters
                    filtered_data = self._apply_filters(data, kwargs)
                    
                    # Calculate scores
                    scores = self._calculate_scores(filtered_data, kwargs)
                    
                    # Add to results
                    results.append({
                        'symbol': symbol,
                        'data': filtered_data,
                        'scores': scores
                    })
                except Exception as e:
                    print(f"Error screening {symbol}: {str(e)}")
            
            return results
        
        def _apply_filters(self, data: pd.DataFrame, kwargs: Dict[str, Any]) -> pd.DataFrame:
            """Apply filtering criteria to the data."""
            filtered_data = data.copy()
            for filter_name, filter_value in kwargs.items():
                if filter_name in self.screener_config.get('filters', {}):
                    filter_func = getattr(self, f'_filter_{filter_name}', None)
                    if filter_func:
                        filtered_data = filter_func(filtered_data, filter_value)
            return filtered_data
        
        def _calculate_scores(self, data: pd.DataFrame, kwargs: Dict[str, Any]) -> Dict[str, float]:
            """Calculate scores for the data."""
            scores = {}
            for score_name, score_config in self.screener_config.get('scores', {}).items():
                score_func = getattr(self, f'_score_{score_name}', None)
                if score_func:
                    scores[score_name] = score_func(data, **score_config)
            return scores
        
        def _filter_price(self, data: pd.DataFrame, price_range: List[float]) -> pd.DataFrame:
            """Filter data by price range."""
            return data[(data['close'] >= price_range[0]) & (data['close'] <= price_range[1])]
        
        def _score_volume(self, data: pd.DataFrame, period: int = 20) -> float:
            """Calculate average volume score."""
            return data['volume'].rolling(window=period).mean().iloc[-1] / data['volume'].iloc[-1]
        
        def _score_momentum(self, data: pd.DataFrame, period: int = 50) -> float:
            """Calculate momentum score."""
            return (data['close'].iloc[-1] / data['close'].iloc[-period-1]) - 1
    ```
  - Tách logic filter và scoring thành các module riêng
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Module Screener hoạt động ổn định với các filter và score
- **Thời gian ước tính**: 3 ngày

#### Tái cấu trúc VolumeSkipeBreakout
- **Cách tiếp cận**: Chuyển đổi detector.py sang module chiến lược
- **Công cụ**: pandas, numpy
- **Bước thực hiện**:
  - Tạo abstract class `Strategy` trong `src/core_engine/strategies/base.py`:
    ```python
    # src/core_engine/strategies/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Dict, Any, Optional
    
    class Strategy(ABC):
        """Base class for all trading strategies."""
        
        @abstractmethod
        def apply(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Apply the strategy to the data.
            
            Args:
                data: DataFrame with historical data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with strategy results
            """
            pass
    ```
  - Implement `VolumeBreakoutStrategy` trong `src/core_engine/strategies/volume_breakout.py`:
    ```python
    # src/core_engine/strategies/volume_breakout.py
    import pandas as pd
    import numpy as np
    from typing import List, Dict, Any
    from datetime import datetime
    
    from src.core_engine.strategies.base import Strategy
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    class VolumeBreakoutStrategy(Strategy):
        """Volume breakout strategy."""
        
        def __init__(self, data_manager: DataManager, config: ConfigManager):
            """Initialize VolumeBreakoutStrategy.
            
            Args:
                data_manager: DataManager instance
                config: ConfigManager instance
            """
            self.data_manager = data_manager
            self.config = config
            self.strategy_config = config.get('strategies.volume_breakout', {})
        
        def apply(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Apply the volume breakout strategy to the data.
            
            Args:
                data: DataFrame with historical data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with strategy results
            """
            results = {}
            for symbol in data['symbol'].unique():
                try:
                    symbol_data = data[data['symbol'] == symbol]
                    breakout = self._detect_breakout(symbol_data, **kwargs)
                    results[symbol] = {
                        'breakout': breakout,
                        'data': symbol_data
                    }
                except Exception as e:
                    print(f"Error applying strategy to {symbol}: {str(e)}")
            return results
        
        def _detect_breakout(self, data: pd.DataFrame, **kwargs) -> bool:
            """Detect volume breakout."""
            volume_ma_period = kwargs.get('volume_ma_period', self.strategy_config.get('volume_ma_period', 10))
            volume_threshold = kwargs.get('volume_threshold', self.strategy_config.get('volume_threshold', 1.5))
            consolidation_days = kwargs.get('consolidation_days', self.strategy_config.get('consolidation_days', 10))
            max_range_percent = kwargs.get('max_range_percent', self.strategy_config.get('max_range_percent', 7.0))
            
            # Calculate volume moving average
            data['volume_ma'] = data['volume'].rolling(window=volume_ma_period).mean()
            
            # Check for breakout
            breakout_condition = (
                (data['volume'] > volume_ma_period * volume_threshold) &
                (data['high'] / data['low'] <= 1 + max_range_percent / 100) &
                (data['close'] > data['open'])
            )
            
            # Find the last breakout
            breakout_index = data[breakout_condition].index[-1] if breakout_condition.any() else None
            
            if breakout_index is None:
                return False
            
            # Check for consolidation
            consolidation_period = data.loc[:breakout_index].tail(consolidation_days)
            if consolidation_period['close'].iloc[-1] < consolidation_period['close'].iloc[0] * (1 - max_range_percent / 100):
                return False
            
            return True
    ```
  - Tách logic tính toán chỉ báo thành module riêng
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Module VolumeBreakoutStrategy hoạt động ổn định
- **Thời gian ước tính**: 3 ngày

#### Xây dựng Risk Manager
- **Cách tiếp cận**: Tạo module quản lý rủi ro mới
- **Công cụ**: pandas, numpy
- **Bước thực hiện**:
  - Tạo abstract class `RiskManager` trong `src/core_engine/risk/base.py`:
    ```python
    # src/core_engine/risk/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Dict, Any, Optional
    
    class RiskManager(ABC):
        """Base class for all risk managers."""
        
        @abstractmethod
        def calculate_risk(self, portfolio: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Calculate risk metrics for the portfolio.
            
            Args:
                portfolio: DataFrame with portfolio data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with risk metrics
            """
            pass
    ```
  - Implement `PositionSizeRiskManager` trong `src/core_engine/risk/position_size.py`:
    ```python
    # src/core_engine/risk/position_size.py
    import pandas as pd
    import numpy as np
    from typing import List, Dict, Any
    from datetime import datetime
    
    from src.core_engine.risk.base import RiskManager
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    class PositionSizeRiskManager(RiskManager):
        """Risk manager for position sizing."""
        
        def __init__(self, data_manager: DataManager, config: ConfigManager):
            """Initialize PositionSizeRiskManager.
            
            Args:
                data_manager: DataManager instance
                config: ConfigManager instance
            """
            self.data_manager = data_manager
            self.config = config
            self.risk_config = config.get('risk', {})
        
        def calculate_risk(self, portfolio: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Calculate position size risk metrics for the portfolio.
            
            Args:
                portfolio: DataFrame with portfolio data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with risk metrics
            """
            max_position_size = kwargs.get('max_position_size', self.risk_config.get('max_position_size', 0.1))
            position_size = portfolio['value'].sum() / portfolio['value'].sum()
            return {
                'position_size': position_size,
                'max_position_size': max_position_size,
                'risk_level': position_size / max_position_size
            }
    ```
  - Implement `StopLossRiskManager` trong `src/core_engine/risk/stop_loss.py`:
    ```python
    # src/core_engine/risk/stop_loss.py
    import pandas as pd
    import numpy as np
    from typing import List, Dict, Any
    from datetime import datetime
    
    from src.core_engine.risk.base import RiskManager
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    class StopLossRiskManager(RiskManager):
        """Risk manager for stop loss."""
        
        def __init__(self, data_manager: DataManager, config: ConfigManager):
            """Initialize StopLossRiskManager.
            
            Args:
                data_manager: DataManager instance
                config: ConfigManager instance
            """
            self.data_manager = data_manager
            self.config = config
            self.risk_config = config.get('risk', {})
        
        def calculate_risk(self, portfolio: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Calculate stop loss risk metrics for the portfolio.
            
            Args:
                portfolio: DataFrame with portfolio data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with risk metrics
            """
            default_stop_loss = kwargs.get('default_stop_loss', self.risk_config.get('default_stop_loss', 0.03))
            stop_loss_levels = portfolio['close'].pct_change().abs().rolling(window=20).quantile(0.9)
            return {
                'stop_loss_levels': stop_loss_levels,
                'default_stop_loss': default_stop_loss
            }
    ```
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Module Risk Manager hoạt động ổn định với các phương pháp rủi ro
- **Thời gian ước tính**: 3 ngày

#### Xây dựng Portfolio Manager
- **Cách tiếp cận**: Tạo module quản lý danh mục đầu tư
- **Công cụ**: pandas, numpy
- **Bước thực hiện**:
  - Tạo abstract class `PortfolioManager` trong `src/core_engine/portfolio/base.py`:
    ```python
    # src/core_engine/portfolio/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Dict, Any, Optional
    
    class PortfolioManager(ABC):
        """Base class for all portfolio managers."""
        
        @abstractmethod
        def manage(self, portfolio: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Manage the portfolio.
            
            Args:
                portfolio: DataFrame with portfolio data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with portfolio management results
            """
            pass
    ```
  - Implement `SimplePortfolioManager` trong `src/core_engine/portfolio/simple.py`:
    ```python
    # src/core_engine/portfolio/simple.py
    import pandas as pd
    import numpy as np
    from typing import List, Dict, Any
    from datetime import datetime
    
    from src.core_engine.portfolio.base import PortfolioManager
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    class SimplePortfolioManager(PortfolioManager):
        """Simple portfolio manager."""
        
        def __init__(self, data_manager: DataManager, config: ConfigManager):
            """Initialize SimplePortfolioManager.
            
            Args:
                data_manager: DataManager instance
                config: ConfigManager instance
            """
            self.data_manager = data_manager
            self.config = config
            self.portfolio_config = config.get('portfolio', {})
        
        def manage(self, portfolio: pd.DataFrame, **kwargs) -> Dict[str, Any]:
            """Manage the portfolio.
            
            Args:
                portfolio: DataFrame with portfolio data
                **kwargs: Additional parameters
                
            Returns:
                Dictionary with portfolio management results
            """
            results = {}
            for symbol in portfolio['symbol'].unique():
                try:
                    symbol_data = portfolio[portfolio['symbol'] == symbol]
                    performance = self._calculate_performance(symbol_data, **kwargs)
                    results[symbol] = {
                        'performance': performance,
                        'data': symbol_data
                    }
                except Exception as e:
                    print(f"Error managing portfolio for {symbol}: {str(e)}")
            return results
        
        def _calculate_performance(self, data: pd.DataFrame, **kwargs) -> Dict[str, float]:
            """Calculate performance metrics for the portfolio."""
            performance = {}
            performance['return'] = (data['close'].iloc[-1] / data['close'].iloc[0]) - 1
            performance['volatility'] = data['close'].pct_change().std()
            performance['sharpe_ratio'] = performance['return'] / performance['volatility']
            return performance
    ```
  - Thêm các phương thức tính toán hiệu suất
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Module Portfolio Manager hoạt động ổn định với các chỉ số hiệu suất
- **Thời gian ước tính**: 3 ngày

#### Xây dựng Paper Broker
- **Cách tiếp cận**: Tạo module mô phỏng giao dịch
- **Công cụ**: pandas, numpy
- **Bước thực hiện**:
  - Tạo abstract class `Broker` trong `src/core_engine/broker/base.py`:
    ```python
    # src/core_engine/broker/base.py
    from abc import ABC, abstractmethod
    import pandas as pd
    from typing import List, Dict, Any, Optional
    
    class Broker(ABC):
        """Base class for all brokers."""
        
        @abstractmethod
        def execute_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
            """Execute an order.
            
            Args:
                order: Dictionary with order details
                
            Returns:
                Dictionary with order execution results
            """
            pass
    ```
  - Implement `PaperBroker` trong `src/core_engine/broker/paper.py`:
    ```python
    # src/core_engine/broker/paper.py
    import pandas as pd
    import numpy as np
    from typing import List, Dict, Any
    from datetime import datetime
    
    from src.core_engine.broker.base import Broker
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    class PaperBroker(Broker):
        """Paper broker for simulation."""
        
        def __init__(self, data_manager: DataManager, config: ConfigManager):
            """Initialize PaperBroker.
            
            Args:
                data_manager: DataManager instance
                config: ConfigManager instance
            """
            self.data_manager = data_manager
            self.config = config
            self.broker_config = config.get('broker', {})
        
        def execute_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
            """Execute an order.
            
            Args:
                order: Dictionary with order details
                
            Returns:
                Dictionary with order execution results
            """
            try:
                symbol = order['symbol']
                quantity = order['quantity']
                price = order['price']
                commission_rate = order.get('commission_rate', self.broker_config.get('commission_rate', 0.0015))
                slippage = order.get('slippage', self.broker_config.get('slippage', 0.001))
                
                # Get current market data
                data = self.data_manager.get_data(symbol, start_date=order['date'], end_date=order['date'])
                if data.empty:
                    return {'error': f"No data available for {symbol} on {order['date']}"}
                
                # Calculate execution price
                execution_price = price * (1 + slippage)
                
                # Calculate commission
                commission = quantity * execution_price * commission_rate
                
                # Calculate net cost
                net_cost = quantity * execution_price + commission
                
                return {
                    'symbol': symbol,
                    'quantity': quantity,
                    'execution_price': execution_price,
                    'commission': commission,
                    'net_cost': net_cost
                }
            except Exception as e:
                return {'error': f"Error executing order: {str(e)}"}
    ```
  - Thêm logic xử lý lệnh và tính toán phí giao dịch
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Module Paper Broker hoạt động ổn định với các tính năng giao dịch
- **Thời gian ước tính**: 3 ngày

### Giai đoạn 4: Xây dựng Interfaces

#### Tạo CLI/API interface
- **Cách tiếp cận**: Tạo giao diện dòng lệnh và REST API
- **Công cụ**: click, FastAPI
- **Bước thực hiện**:
  - Tạo module CLI trong `src/interfaces/cli/commands.py`:
    ```python
    # src/interfaces/cli/commands.py
    import click
    from src.data_layer.data_manager import DataManager
    from src.core_engine.screener.stock_screener import StockScreener
    from src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy
    from src.core_engine.risk.position_size import PositionSizeRiskManager
    from src.core_engine.portfolio.simple import SimplePortfolioManager
    from src.core_engine.broker.paper import PaperBroker
    from src.utils.config import ConfigManager
    
    @click.group()
    def cli():
        """Stock market analysis and trading CLI."""
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def screen(symbols, start_date, end_date):
        """Screen stocks based on multiple criteria."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        screener = StockScreener(data_manager, config)
        symbols = symbols.split(',')
        results = screener.screen(symbols, start_date=start_date, end_date=end_date)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def breakout(symbols, start_date, end_date):
        """Detect volume breakout for stocks."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        strategy = VolumeBreakoutStrategy(data_manager, config)
        symbols = symbols.split(',')
        results = strategy.apply(symbols, start_date=start_date, end_date=end_date)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def risk(symbols, start_date, end_date):
        """Calculate risk metrics for the portfolio."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        risk_manager = PositionSizeRiskManager(data_manager, config)
        portfolio = data_manager.get_data_batch(symbols.split(','), start_date, end_date)
        results = risk_manager.calculate_risk(portfolio)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def portfolio(symbols, start_date, end_date):
        """Manage the portfolio."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        portfolio_manager = SimplePortfolioManager(data_manager, config)
        portfolio = data_manager.get_data_batch(symbols.split(','), start_date, end_date)
        results = portfolio_manager.manage(portfolio)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbol', required=True, help='Stock symbol')
    @click.option('--quantity', required=True, type=int, help='Quantity to buy/sell')
    @click.option('--price', required=True, type=float, help='Price at which to buy/sell')
    @click.option('--date', required=True, help='Date of the order in YYYY-MM-DD format')
    def trade(symbol, quantity, price, date):
        """Execute a paper trade."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        broker = PaperBroker(data_manager, config)
        order = {
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'date': date
        }
        result = broker.execute_order(order)
        click.echo(result)
    ```
  - Tạo module API trong `src/interfaces/api/routes.py`:
    ```python
    # src/interfaces/api/routes.py
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import JSONResponse
    from src.data_layer.data_manager import DataManager
    from src.core_engine.screener.stock_screener import StockScreener
    from src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy
    from src.core_engine.risk.position_size import PositionSizeRiskManager
    from src.core_engine.portfolio.simple import SimplePortfolioManager
    from src.core_engine.broker.paper import PaperBroker
    from src.utils.config import ConfigManager
    
    app = FastAPI()
    
    @app.get("/screen")
    async def screen(symbols: str, start_date: str, end_date: str):
        """Screen stocks based on multiple criteria."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        screener = StockScreener(data_manager, config)
        symbols = symbols.split(',')
        results = screener.screen(symbols, start_date=start_date, end_date=end_date)
        return JSONResponse(content=results)
    
    @app.get("/breakout")
    async def breakout(symbols: str, start_date: str, end_date: str):
        """Detect volume breakout for stocks."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        strategy = VolumeBreakoutStrategy(data_manager, config)
        symbols = symbols.split(',')
        results = strategy.apply(symbols, start_date=start_date, end_date=end_date)
        return JSONResponse(content=results)
    
    @app.get("/risk")
    async def risk(symbols: str, start_date: str, end_date: str):
        """Calculate risk metrics for the portfolio."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        risk_manager = PositionSizeRiskManager(data_manager, config)
        portfolio = data_manager.get_data_batch(symbols.split(','), start_date, end_date)
        results = risk_manager.calculate_risk(portfolio)
        return JSONResponse(content=results)
    
    @app.get("/portfolio")
    async def portfolio(symbols: str, start_date: str, end_date: str):
        """Manage the portfolio."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        portfolio_manager = SimplePortfolioManager(data_manager, config)
        portfolio = data_manager.get_data_batch(symbols.split(','), start_date, end_date)
        results = portfolio_manager.manage(portfolio)
        return JSONResponse(content=results)
    
    @app.post("/trade")
    async def trade(symbol: str, quantity: int, price: float, date: str):
        """Execute a paper trade."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        broker = PaperBroker(data_manager, config)
        order = {
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'date': date
        }
        result = broker.execute_order(order)
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        return JSONResponse(content=result)
    ```
  - Thêm các lệnh và endpoint cơ bản
  - Viết unit test cho các lệnh và endpoint
- **Kết quả mong đợi**: CLI và API hoạt động ổn định với các chức năng
- **Thời gian ước tính**: 4 ngày

#### Tích hợp Grafana
- **Cách tiếp cận**: Tạo data source cho Grafana
- **Công cụ**: Grafana, FastAPI
- **Bước thực hiện**:
  - Tạo module Grafana trong `src/interfaces/visualization/grafana.py`:
    ```python
    # src/interfaces/visualization/grafana.py
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import JSONResponse
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    app = FastAPI()
    
    @app.get("/grafana/data")
    async def get_grafana_data(symbol: str, start_date: str, end_date: str):
        """Get data for Grafana visualization."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        data = data_manager.get_data(symbol, start_date=start_date, end_date=end_date)
        if data.empty:
            raise HTTPException(status_code=404, detail=f"No data available for {symbol} from {start_date} to {end_date}")
        return JSONResponse(content=data.to_dict(orient='records'))
    ```
  - Tạo các endpoint API để cung cấp dữ liệu cho Grafana
  - Tạo các dashboard mẫu
  - Viết tài liệu hướng dẫn cài đặt và sử dụng
- **Kết quả mong đợi**: Grafana hoạt động ổn định với các dashboard
- **Thời gian ước tính**: 3 ngày

#### Chuẩn bị kết nối VN Broker API
- **Cách tiếp cận**: Tạo adapter cho VN Broker API
- **Công cụ**: requests, FastAPI
- **Bước thực hiện**:
  - Tạo module VN Broker trong `src/interfaces/broker/vn_broker.py`:
    ```python
    # src/interfaces/broker/vn_broker.py
    import requests
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import JSONResponse
    from src.data_layer.data_manager import DataManager
    from src.utils.config import ConfigManager
    
    app = FastAPI()
    
    @app.get("/vnbroker/data")
    async def get_vnbroker_data(symbol: str, start_date: str, end_date: str):
        """Get data from VN Broker API."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        data = data_manager.get_data(symbol, start_date=start_date, end_date=end_date)
        if data.empty:
            raise HTTPException(status_code=404, detail=f"No data available for {symbol} from {start_date} to {end_date}")
        return JSONResponse(content=data.to_dict(orient='records'))
    ```
  - Implement các phương thức kết nối và giao dịch
  - Thêm xác thực và bảo mật
  - Viết unit test cho các phương thức
- **Kết quả mong đợi**: Kết nối với VN Broker API hoạt động ổn định
- **Thời gian ước tính**: 3 ngày

### Giai đoạn 5: Tích hợp và Kiểm thử

#### Tích hợp tất cả các module
- **Cách tiếp cận**: Tạo các script tích hợp
- **Công cụ**: Python
- **Bước thực hiện**:
  - Tạo script chính trong `src/main.py`:
    ```python
    # src/main.py
    import click
    from src.data_layer.data_manager import DataManager
    from src.core_engine.screener.stock_screener import StockScreener
    from src.core_engine.strategies.volume_breakout import VolumeBreakoutStrategy
    from src.core_engine.risk.position_size import PositionSizeRiskManager
    from src.core_engine.portfolio.simple import SimplePortfolioManager
    from src.core_engine.broker.paper import PaperBroker
    from src.utils.config import ConfigManager
    
    @click.group()
    def cli():
        """Stock market analysis and trading CLI."""
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def screen(symbols, start_date, end_date):
        """Screen stocks based on multiple criteria."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        screener = StockScreener(data_manager, config)
        symbols = symbols.split(',')
        results = screener.screen(symbols, start_date=start_date, end_date=end_date)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def breakout(symbols, start_date, end_date):
        """Detect volume breakout for stocks."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        strategy = VolumeBreakoutStrategy(data_manager, config)
        symbols = symbols.split(',')
        results = strategy.apply(symbols, start_date=start_date, end_date=end_date)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def risk(symbols, start_date, end_date):
        """Calculate risk metrics for the portfolio."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        risk_manager = PositionSizeRiskManager(data_manager, config)
        portfolio = data_manager.get_data_batch(symbols.split(','), start_date, end_date)
        results = risk_manager.calculate_risk(portfolio)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbols', required=True, help='Comma-separated list of stock symbols')
    @click.option('--start-date', required=True, help='Start date in YYYY-MM-DD format')
    @click.option('--end-date', required=True, help='End date in YYYY-MM-DD format')
    def portfolio(symbols, start_date, end_date):
        """Manage the portfolio."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        portfolio_manager = SimplePortfolioManager(data_manager, config)
        portfolio = data_manager.get_data_batch(symbols.split(','), start_date, end_date)
        results = portfolio_manager.manage(portfolio)
        click.echo(results)
    
    @cli.command()
    @click.option('--symbol', required=True, help='Stock symbol')
    @click.option('--quantity', required=True, type=int, help='Quantity to buy/sell')
    @click.option('--price', required=True, type=float, help='Price at which to buy/sell')
    @click.option('--date', required=True, help='Date of the order in YYYY-MM-DD format')
    def trade(symbol, quantity, price, date):
        """Execute a paper trade."""
        config = ConfigManager()
        data_manager = DataManager(config=config)
        broker = PaperBroker(data_manager, config)
        order = {
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'date': date
        }
        result = broker.execute_order(order)
        click.echo(result)
    ```
  - Tạo các script chạy riêng cho từng chức năng
  - Thêm logging và error handling
  - Viết tài liệu hướng dẫn sử dụng
- **Kết quả mong đợi**: Tích hợp tất cả các module thành công
- **Thời gian ước tính**: 3 ngày

#### Viết test end-to-end
- **Cách tiếp cận**: Tạo các test case end-to-end
- **Công cụ**: pytest
- **Bước thực hiện**:
  - Tạo test case trong `tests/integration/`
  - Thêm các scenario test khác nhau
  - Tạo mock data cho testing
  - Thiết lập CI/CD để chạy test tự động
- **Kết quả mong đợi**: Test end-to-end hoạt động ổn định
- **Thời gian ước tính**: 3 ngày

#### Tối ưu hiệu suất
- **Cách tiếp cận**: Profiling và tối ưu code
- **Công cụ**: cProfile, line_profiler
- **Bước thực hiện**:
  - Chạy profiling trên các chức năng chính
  - Xác định và tối ưu bottleneck
  - Thêm caching và parallel processing
  - Benchmark trước và sau tối ưu
- **Kết quả mong đợi**: Hiệu suất hệ thống được cải thiện đáng kể
- **Thời gian ước tính**: 3 ngày

## 3. Thứ tự Ưu tiên các Module cần Refactor

1. **Data Layer - VNDataFetcher**: Đây là nền tảng cho toàn bộ hệ thống, cần được ưu tiên đầu tiên
2. **Core Engine - Screener & VolumeSkipeBreakout**: Chức năng cốt lõi hiện tại của hệ thống
3. **Interfaces - CLI**: Cung cấp giao diện người dùng cơ bản
4. **Core Engine - Risk Manager**: Quản lý rủi ro cho các chiến lược
5. **Core Engine - Portfolio Manager**: Quản lý danh mục đầu tư
6. **Data Layer - DuckDB/Parquet**: Cải thiện hiệu suất lưu trữ
7. **Core Engine - Paper Broker**: Mô phỏng giao dịch
8. **Interfaces - Grafana**: Visualization nâng cao
9. **Interfaces - VN Broker API**: Kết nối với sàn giao dịch thực

## 4. Các Rủi ro Tiềm ẩn và Cách Giảm thiểu

### Rủi ro 1: Mất tính năng hiện có trong quá trình refactoring
- **Giảm thiểu**:
  - Viết test case đầy đủ trước khi refactor
  - Refactor từng phần nhỏ, không thay đổi toàn bộ cùng lúc
  - Sử dụng feature flags để duy trì cả code cũ và mới song song
  - Tạo branch riêng cho mỗi phần refactor và merge khi đã test kỹ

### Rủi ro 2: Hiệu suất giảm sau khi refactor
- **Giảm thiểu**:
  - Benchmark hiệu suất trước và sau refactor
  - Tối ưu các phần quan trọng như data fetching và processing
  - Sử dụng profiling để xác định bottleneck
  - Thêm caching ở các điểm quan trọng
  - Sử dụng DuckDB/Parquet để tối ưu hiệu suất truy vấn dữ liệu

### Rủi ro 3: Tích hợp các module mới gặp khó khăn
- **Giảm thiểu**:
  - Thiết kế interface rõ ràng giữa các module
  - Viết integration test
  - Áp dụng Dependency Injection để giảm sự phụ thuộc trực tiếp
  - Tạo documentation chi tiết cho mỗi module
  - Sử dụng type hints để giảm lỗi khi tích hợp

### Rủi ro 4: Khó khăn khi chuyển đổi từ code hiện tại sang kiến trúc mới
- **Giảm thiểu**:
  - Tạo adapter pattern để kết nối code cũ và mới
  - Refactor từng phần nhỏ, có thể rollback nếu cần
  - Duy trì tài liệu chi tiết về quá trình chuyển đổi
  - Tạo migration script để chuyển đổi dữ liệu
  - Sử dụng feature toggles để chuyển đổi dần dần

### Rủi ro 5: Thay đổi API có thể ảnh hưởng đến người dùng hiện tại
- **Giảm thiểu**:
  - Duy trì backward compatibility
  - Cung cấp tài liệu migration guide
  - Sử dụng versioning cho API
  - Thông báo trước cho người dùng về các thay đổi
  - Cung cấp các ví dụ code mới

### Rủi ro 6: Phụ thuộc vào thư viện bên thứ ba
- **Giảm thiểu**:
  - Tạo adapter/wrapper cho các thư viện bên thứ ba
  - Kiểm tra kỹ các thư viện trước khi sử dụng
  - Có kế hoạch dự phòng nếu thư viện không được duy trì
  - Giới hạn phạm vi sử dụng thư viện bên thứ ba
  - Cân nhắc fork và duy trì nếu cần thiết

### Rủi ro 7: Quản lý dữ liệu lớn
- **Giảm thiểu**:
  - Sử dụng DuckDB/Parquet để xử lý dữ liệu hiệu quả
  - Implement cơ chế phân trang và lazy loading
  - Tối ưu hóa schema và index
  - Sử dụng caching thông minh
  - Xây dựng cơ chế clean up dữ liệu cũ
