from dataclasses import dataclass

@dataclass
class BreakoutConfig:
    """<PERSON><PERSON><PERSON> hình cho hệ thống phát hiện Volume Spike Breakout"""
    
    # Các thông số cơ bản
    volume_ma_period: int = 10  # Số phiên để tính trung bình khối lượng
    volume_threshold: float = 1.5  # Ngưỡng volume spike (1.5 = 150% trung bình)
    candle_body_ratio: float = 0.7  # Tỷ lệ thân nến tối thiểu (70% tổng chiều dài)
    lookback_period: int = 5  # Số phiên nhìn lại để tìm đỉnh gần nhất
    consolidation_threshold: float = 0.07  # Ngưỡng tích lũy (7%)
    
    # C<PERSON>c thông số phân tích Volume Profile
    poc_distance_threshold: float = 0.15  # Ngưỡng khoảng cách với POC (15%)
    volume_profile_bins: int = 50  # Số khoảng giá để tính Volume Profile
    
    # Cá<PERSON> thông số quản lý rủi ro
    stop_loss_percent: float = 0.03  # Mức stop loss (3% dưới giá vào)
    risk_reward_ratio: float = 2.0  # Tỷ lệ lợi nhuận/rủi ro tối thiểu
    max_intraday_gain: float = 0.05  # Ngưỡng tăng tối đa trong phiên (5%)
    
    # Các thông số cho giao dịch
    position_size_ratio: float = 0.5  # Tỷ lệ vị thế ban đầu (50% tổng vị thế)
    
    # Các thông số cho tích lũy
    consolidation_days: int = 10  # Số phiên tích lũy
    max_range_percent: float = 7.0  # Biên độ tích lũy tối đa (%)
    
    # Các thông số cho lọc cổ phiếu
    min_price: float = 10000  # Giá tối thiểu
    min_volume: float = 100000  # Khối lượng tối thiểu
