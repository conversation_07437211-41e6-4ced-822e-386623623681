from vnstock.explorer.tcbs.quote import Quote
from datetime import datetime, timedelta
from volume_skipe_breakout.config import BreakoutConfig
from volume_skipe_breakout.detector import VolumeBreakoutDetector
import pandas as pd

def analyze_stock(symbol: str) -> None:
    """Phân tích Volume Breakout cho một mã cổ phiếu"""
    
    # Khởi tạo các thành phần
    config = BreakoutConfig(
        volume_ma_period=10,
        volume_threshold=1.5,
        candle_body_ratio=0.7,
        lookback_period=5,
        poc_distance_threshold=0.15,
        stop_loss_percent=0.03,
        risk_reward_ratio=2.0
    )
    detector = VolumeBreakoutDetector(config)

    # Thiết lập khoảng thời gian (30 ngày gần nhất)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    try:
        # L<PERSON>y dữ liệu cho mã cổ phiếu
        quote = Quote(symbol=symbol)
        data = quote.history(start=start_date.strftime('%Y-%m-%d'), 
                           end=end_date.strftime('%Y-%m-%d'), 
                           interval="1D")

        if data.empty:
            print(f"\n❌ Không có dữ liệu cho mã {symbol}")
            return

        # Lấy tín hiệu giao dịch
        signals = detector.get_trading_signals(data)

        # In kết quả phân tích
        print(f"\n=== Phân tích Volume Breakout cho {symbol} ===")
        print(f"Thời gian: {signals['thoi_gian']}")
        print(f"Giá hiện tại: {signals['gia']:.2f}")
        print(f"Tỷ lệ khối lượng: {signals['ty_le_volume']:.2f}x trung bình")
        print(f"\nPhát hiện Breakout: {'Có' if signals['co_breakout'] else 'Không'}")
        
        print("\nPhân tích vị trí giá:")
        print(f"Vị trí so với POC: {signals['phan_tich_poc']['vi_tri']}")
        print(f"Khoảng cách từ POC: {signals['phan_tich_poc']['khoang_cach_poc']:.2%}")
        
        print("\nChecklist giao dịch:")
        for item, status in signals['checklist'].items():
            print(f"✓ {item}: {'Có' if status else 'Không'}")
        
        print("\nKhuyến nghị giao dịch:")
        rec = signals['khuyen_nghi']
        print(f"Hành động: {rec['hanh_dong']}")
        if 'ly_do' in rec:
            print(f"Lý do: {rec['ly_do']}")
        if 'gia_vao' in rec:
            print(f"Giá vào lệnh: {rec['gia_vao']:.2f}")
            print(f"Stop loss: {rec['stop_loss']:.2f}")
            print(f"Mục tiêu: {rec['muc_tieu']:.2f}")
            print(f"Tỷ lệ vị thế: {rec['ty_le_vi_the']:.0%}")
            
    except Exception as e:
        print(f"\n❌ Lỗi khi phân tích mã {symbol}: {str(e)}")

def main():
    while True:
        # Nhập mã cổ phiếu
        symbol = input("\nNhập mã cổ phiếu (hoặc 'q' để thoát): ").strip().upper()
        
        if symbol == 'Q':
            print("\nTạm biệt! 👋")
            break
            
        if not symbol:
            print("\n⚠️ Vui lòng nhập mã cổ phiếu!")
            continue
            
        # Phân tích mã cổ phiếu
        analyze_stock(symbol)
        
        # Hỏi có muốn tiếp tục không
        choice = input("\nBạn có muốn phân tích mã khác không? (y/n): ").strip().lower()
        if choice != 'y':
            print("\nTạm biệt! 👋")
            break

if __name__ == "__main__":
    main()
