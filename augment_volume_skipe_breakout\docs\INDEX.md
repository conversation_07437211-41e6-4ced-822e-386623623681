# Tài liệu dự án Algorithmic Trading

Đây là trang chỉ mục chính cho tất cả tài liệu của dự án. Sử dụng trang này để điều hướng đến các tài liệu chi tiết.

## Tài liệu có sẵn

| <PERSON><PERSON><PERSON> li<PERSON> | <PERSON><PERSON> tả | Kích thước |
|----------|-------|------------|
| [ARCHITECTURE.md](./ARCHITECTURE.md) | Kiến trúc tổng thể của hệ thống | 10.2 KB |
| [PRODUCT.md](./PRODUCT.md) | Roadmap sản phẩm và kế hoạch triển khai | 4.7 KB |
| [STRATEGIES.md](./STRATEGIES.md) | Chi tiết về các chiến lược giao dịch | 7.9 KB |
| [API.md](./API.md) | Tài liệu API của các module | 6.1 KB |
| [DEVELOPMENT.md](./DEVELOPMENT.md) | Hướng dẫn phát triển và quy tắc code | 3.4 KB |
| [DECISIONS.md](./DECISIONS.md) | Các quyết định kiến trúc quan trọng (ADRs) | 3.8 KB |

## Hướng dẫn sử dụng

### Dành cho nhà phát triển mới

Nếu bạn mới tham gia dự án, hãy đọc các tài liệu theo thứ tự sau:

1. **ARCHITECTURE.md** - Để hiểu tổng quan về hệ thống
2. **DEVELOPMENT.md** - Để thiết lập môi trường phát triển
3. **PRODUCT.md** - Để hiểu roadmap và kế hoạch triển khai
4. **API.md** - Để tìm hiểu cách sử dụng các module

### Dành cho nhà phát triển chiến lược

Nếu bạn muốn phát triển chiến lược giao dịch mới:

1. **STRATEGIES.md** - Để hiểu cách chiến lược được triển khai
2. **API.md** - Để hiểu cách sử dụng các module dữ liệu và chỉ báo

### Dành cho kiến trúc sư

Nếu bạn cần hiểu hoặc đóng góp vào kiến trúc hệ thống:

1. **ARCHITECTURE.md** - Để hiểu kiến trúc hiện tại
2. **DECISIONS.md** - Để hiểu các quyết định kiến trúc đã được đưa ra
3. **PRODUCT.md** - Để hiểu roadmap và kế hoạch triển khai

## Quy trình cập nhật tài liệu

1. Khi thực hiện thay đổi kiến trúc, hãy cập nhật ARCHITECTURE.md
2. Khi đưa ra quyết định kiến trúc quan trọng, hãy thêm ADR mới vào DECISIONS.md
3. Khi thêm hoặc thay đổi API, hãy cập nhật API.md
4. Khi thêm chiến lược mới, hãy cập nhật STRATEGIES.md

Cập nhật lần cuối: 07/04/2025
