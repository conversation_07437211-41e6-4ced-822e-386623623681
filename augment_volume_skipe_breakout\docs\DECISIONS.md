# Quyết định kiến trúc

Tài liệu này ghi lại các quyết định kiến trúc quan trọng được đưa ra trong quá trình phát triển dự án.

## C<PERSON>ch sử dụng

Mỗi quyết định kiến trúc được ghi lại theo mẫu sau:

```
# ADR-XXXX: Tiêu đề quyết định

## Ngày: YYYY-MM-DD

## Trạng thái
[Đã chấp nhận/Đang xem xét/Đã từ chối/Đã thay thế bởi ADR-XXXX]

## Bối cảnh
Mô tả bối cảnh và vấn đề cần giải quyết.

## Các phương án
- Phương án 1: ...
- Phương án 2: ...
- ...

## Quyết định
Quyết định cuối cùng đã được đưa ra.

## Lý do
Giải thích lý do tại sao quyết định này được đưa ra.

## Hậu quả
Mô tả các tác động tích cực và tiêu cực của quyết định.

## Người tham gia
Liệt kê những người tham gia vào quá trình ra quyết định.
```

---

## ADR-0001: Sử dụng vnstock làm nguồn dữ liệu chính

### Ngày: 2025-04-07

### Trạng thái
Đã chấp nhận

### Bối cảnh
Dự án cần một nguồn dữ liệu đáng tin cậy cho thị trường chứng khoán Việt Nam. Các yêu cầu bao gồm:
- Dữ liệu lịch sử giá và khối lượng
- Thông tin công ty
- Cập nhật đều đặn
- Khả năng truy cập API ổn định

### Các phương án
- Phương án 1: Sử dụng vnstock API
- Phương án 2: Sử dụng Yahoo Finance API
- Phương án 3: Xây dựng web scraper riêng

### Quyết định
Sử dụng vnstock làm nguồn dữ liệu chính, với hai phương pháp truy cập:
1. Import trực tiếp: `import vnstock`
2. Sử dụng Quote object: `from vnstock.explorer.tcbs.quote import Quote`

### Lý do
- vnstock cung cấp dữ liệu đặc thù cho thị trường Việt Nam
- API có tài liệu tốt và được cập nhật thường xuyên
- Hỗ trợ nhiều loại dữ liệu (giá, khối lượng, thông tin công ty)
- Cộng đồng người dùng tích cực

### Hậu quả
- Tích cực:
  - Truy cập dễ dàng đến dữ liệu thị trường Việt Nam
  - Giảm thời gian phát triển
- Tiêu cực:
  - Phụ thuộc vào bên thứ ba
  - Cần xử lý rate limiting (thêm delay giữa các lần gọi API)
  - Cần xây dựng cơ chế cache để giảm số lượng request

### Người tham gia
- Team phát triển
- Solution architect

---

## ADR-0002: Thiết kế cơ chế cache thông minh

### Ngày: 2025-04-07

### Trạng thái
Đã chấp nhận

### Bối cảnh
Khi lấy dữ liệu từ API vnstock, chúng ta gặp phải hai vấn đề:
1. Rate limiting - API giới hạn số lượng request trong một khoảng thời gian
2. Hiệu suất - Việc lấy dữ liệu nhiều lần cho cùng một mã chứng khoán làm chậm ứng dụng

### Các phương án
- Phương án 1: Cache đơn giản dựa trên file
- Phương án 2: Sử dụng cơ sở dữ liệu SQLite
- Phương án 3: Cache thông minh với metadata và quản lý hết hạn

### Quyết định
Triển khai cơ chế SmartCache với các tính năng:
- Lưu trữ dữ liệu dưới dạng file JSON
- Theo dõi metadata (thời gian lấy, thời gian hết hạn)
- Tự động làm mới dữ liệu khi hết hạn
- Quản lý dung lượng cache

### Lý do
- Giảm số lượng request API, tránh rate limiting
- Cải thiện hiệu suất ứng dụng
- Vẫn đảm bảo dữ liệu được cập nhật khi cần

### Hậu quả
- Tích cực:
  - Giảm thời gian phản hồi
  - Ứng dụng vẫn hoạt động khi không có kết nối internet
- Tiêu cực:
  - Tăng độ phức tạp của code
  - Cần quản lý dung lượng đĩa

### Người tham gia
- Team phát triển
- Solution architect
